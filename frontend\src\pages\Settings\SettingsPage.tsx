// TrustVault - Settings Page

import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Grid,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Button,
  Divider,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import {
  Settings,
  Notifications,
  Security,
  Palette,
  Language,
  Delete,
  Save,
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-hot-toast';
import { useMutation, useQueryClient } from 'react-query';

// Store
import { useAuthStore } from '../../store/authStore';

// Services
import apiService from '../../services/api';

interface UserSettings {
  timezone: string;
  language: string;
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    email: boolean;
    push: boolean;
    security: boolean;
    portfolio: boolean;
  };
  privacy: {
    profile_visibility: 'public' | 'private';
    portfolio_visibility: 'public' | 'private';
  };
}

const SettingsPage: React.FC = () => {
  const { user, updateProfile } = useAuthStore();
  const queryClient = useQueryClient();

  const [settings, setSettings] = useState<UserSettings>({
    timezone: user?.timezone || 'UTC',
    language: user?.language || 'en',
    theme: 'light',
    notifications: {
      email: true,
      push: true,
      security: true,
      portfolio: true,
    },
    privacy: {
      profile_visibility: 'private',
      portfolio_visibility: 'private',
    },
  });

  const [deleteAccountDialog, setDeleteAccountDialog] = useState(false);
  const [deleteConfirmation, setDeleteConfirmation] = useState('');

  const updateSettingsMutation = useMutation(
    (data: Partial<UserSettings>) => apiService.updateProfile(data),
    {
      onSuccess: () => {
        toast.success('Settings updated successfully');
        queryClient.invalidateQueries('user-profile');
      },
      onError: () => {
        toast.error('Failed to update settings');
      },
    }
  );

  const handleSettingChange = (category: keyof UserSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: typeof prev[category] === 'object' 
        ? { ...prev[category], [key]: value }
        : value
    }));
  };

  const handleSaveSettings = () => {
    updateSettingsMutation.mutate(settings);
  };

  const timezones = [
    { value: 'UTC', label: 'UTC' },
    { value: 'America/New_York', label: 'Eastern Time' },
    { value: 'America/Chicago', label: 'Central Time' },
    { value: 'America/Denver', label: 'Mountain Time' },
    { value: 'America/Los_Angeles', label: 'Pacific Time' },
    { value: 'Europe/London', label: 'London' },
    { value: 'Europe/Paris', label: 'Paris' },
    { value: 'Asia/Tokyo', label: 'Tokyo' },
  ];

  const languages = [
    { value: 'en', label: 'English' },
    { value: 'fr', label: 'Français' },
    { value: 'es', label: 'Español' },
    { value: 'de', label: 'Deutsch' },
  ];

  return (
    <>
      <Helmet>
        <title>Settings - TrustVault</title>
        <meta name="description" content="Manage your account settings and preferences" />
      </Helmet>

      <Box>
        {/* Header */}
        <Box mb={4}>
          <Typography variant="h4" component="h1" gutterBottom>
            Settings
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your account preferences and security settings
          </Typography>
        </Box>

        <Grid container spacing={3}>
          {/* General Settings */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={3}>
                  <Settings color="primary" />
                  <Typography variant="h6">General</Typography>
                </Box>

                <Box display="flex" flexDirection="column" gap={3}>
                  <FormControl fullWidth>
                    <InputLabel>Timezone</InputLabel>
                    <Select
                      value={settings.timezone}
                      onChange={(e) => handleSettingChange('timezone', '', e.target.value)}
                      label="Timezone"
                    >
                      {timezones.map((tz) => (
                        <MenuItem key={tz.value} value={tz.value}>
                          {tz.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <FormControl fullWidth>
                    <InputLabel>Language</InputLabel>
                    <Select
                      value={settings.language}
                      onChange={(e) => handleSettingChange('language', '', e.target.value)}
                      label="Language"
                    >
                      {languages.map((lang) => (
                        <MenuItem key={lang.value} value={lang.value}>
                          {lang.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <FormControl fullWidth>
                    <InputLabel>Theme</InputLabel>
                    <Select
                      value={settings.theme}
                      onChange={(e) => handleSettingChange('theme', '', e.target.value)}
                      label="Theme"
                    >
                      <MenuItem value="light">Light</MenuItem>
                      <MenuItem value="dark">Dark</MenuItem>
                      <MenuItem value="auto">Auto</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Notification Settings */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={3}>
                  <Notifications color="primary" />
                  <Typography variant="h6">Notifications</Typography>
                </Box>

                <List>
                  <ListItem>
                    <ListItemText
                      primary="Email Notifications"
                      secondary="Receive updates via email"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.notifications.email}
                        onChange={(e) => handleSettingChange('notifications', 'email', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>

                  <ListItem>
                    <ListItemText
                      primary="Push Notifications"
                      secondary="Browser push notifications"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.notifications.push}
                        onChange={(e) => handleSettingChange('notifications', 'push', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>

                  <ListItem>
                    <ListItemText
                      primary="Security Alerts"
                      secondary="Important security notifications"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.notifications.security}
                        onChange={(e) => handleSettingChange('notifications', 'security', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>

                  <ListItem>
                    <ListItemText
                      primary="Portfolio Updates"
                      secondary="Portfolio performance notifications"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.notifications.portfolio}
                        onChange={(e) => handleSettingChange('notifications', 'portfolio', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Privacy Settings */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={3}>
                  <Security color="primary" />
                  <Typography variant="h6">Privacy</Typography>
                </Box>

                <Box display="flex" flexDirection="column" gap={3}>
                  <FormControl fullWidth>
                    <InputLabel>Profile Visibility</InputLabel>
                    <Select
                      value={settings.privacy.profile_visibility}
                      onChange={(e) => handleSettingChange('privacy', 'profile_visibility', e.target.value)}
                      label="Profile Visibility"
                    >
                      <MenuItem value="public">Public</MenuItem>
                      <MenuItem value="private">Private</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControl fullWidth>
                    <InputLabel>Portfolio Visibility</InputLabel>
                    <Select
                      value={settings.privacy.portfolio_visibility}
                      onChange={(e) => handleSettingChange('privacy', 'portfolio_visibility', e.target.value)}
                      label="Portfolio Visibility"
                    >
                      <MenuItem value="public">Public</MenuItem>
                      <MenuItem value="private">Private</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Danger Zone */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={3}>
                  <Delete color="error" />
                  <Typography variant="h6" color="error">Danger Zone</Typography>
                </Box>

                <Alert severity="warning" sx={{ mb: 2 }}>
                  These actions cannot be undone. Please be careful.
                </Alert>

                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<Delete />}
                  onClick={() => setDeleteAccountDialog(true)}
                  fullWidth
                >
                  Delete Account
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Save Button */}
        <Box mt={4} display="flex" justifyContent="flex-end">
          <Button
            variant="contained"
            startIcon={<Save />}
            onClick={handleSaveSettings}
            disabled={updateSettingsMutation.isLoading}
          >
            {updateSettingsMutation.isLoading ? 'Saving...' : 'Save Settings'}
          </Button>
        </Box>

        {/* Delete Account Dialog */}
        <Dialog
          open={deleteAccountDialog}
          onClose={() => setDeleteAccountDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle color="error">Delete Account</DialogTitle>
          <DialogContent>
            <Alert severity="error" sx={{ mb: 2 }}>
              This action will permanently delete your account and all associated data.
              This cannot be undone.
            </Alert>
            
            <Typography variant="body2" gutterBottom>
              To confirm, please type "DELETE" in the field below:
            </Typography>
            
            <TextField
              fullWidth
              value={deleteConfirmation}
              onChange={(e) => setDeleteConfirmation(e.target.value)}
              placeholder="Type DELETE to confirm"
              sx={{ mt: 2 }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteAccountDialog(false)}>
              Cancel
            </Button>
            <Button
              color="error"
              variant="contained"
              disabled={deleteConfirmation !== 'DELETE'}
              onClick={() => {
                // Handle account deletion
                toast.error('Account deletion not implemented yet');
                setDeleteAccountDialog(false);
              }}
            >
              Delete Account
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </>
  );
};

export default SettingsPage;
