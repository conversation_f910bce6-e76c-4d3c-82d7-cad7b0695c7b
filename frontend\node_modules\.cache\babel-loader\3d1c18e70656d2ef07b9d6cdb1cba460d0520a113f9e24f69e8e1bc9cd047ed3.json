{"ast": null, "code": "// TrustVault - API Service\n\nimport axios from 'axios';\nimport { toast } from 'react-hot-toast';\n\n// Types\n\nclass ApiService {\n  constructor() {\n    this.api = void 0;\n    this.baseURL = void 0;\n    this.baseURL = process.env.REACT_APP_API_URL || '/api/v1';\n    this.api = axios.create({\n      baseURL: this.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    this.setupInterceptors();\n  }\n  setupInterceptors() {\n    // Request interceptor\n    this.api.interceptors.request.use(config => {\n      // Add auth token if available\n      const token = this.getAccessToken();\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n\n      // Add security headers\n      config.headers['X-Requested-With'] = 'XMLHttpRequest';\n      config.headers['X-Client-Version'] = '1.0.0';\n\n      // Log request in development\n      if (process.env.NODE_ENV === 'development') {\n        var _config$method;\n        console.log(`API Request: ${(_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase()} ${config.url}`);\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // Response interceptor\n    this.api.interceptors.response.use(response => {\n      return response;\n    }, async error => {\n      var _error$response;\n      const originalRequest = error.config;\n\n      // Handle 401 errors (unauthorized)\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n          // Try to refresh token\n          const refreshToken = this.getRefreshToken();\n          if (refreshToken) {\n            const response = await this.refreshAccessToken(refreshToken);\n            this.setTokens(response.data.access_token, refreshToken);\n\n            // Retry original request\n            originalRequest.headers.Authorization = `Bearer ${response.data.access_token}`;\n            return this.api(originalRequest);\n          }\n        } catch (refreshError) {\n          // Refresh failed, redirect to login\n          this.clearTokens();\n          window.location.href = '/login';\n          return Promise.reject(refreshError);\n        }\n      }\n\n      // Handle other errors\n      this.handleApiError(error);\n      return Promise.reject(error);\n    });\n  }\n  handleApiError(error) {\n    var _error$response2, _error$response2$data, _error$response3;\n    const message = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message || 'An error occurred';\n\n    // Don't show toast for certain errors\n    const silentErrors = [401, 403];\n    if (!silentErrors.includes((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status)) {\n      toast.error(message);\n    }\n\n    // Log error in development\n    if (process.env.NODE_ENV === 'development') {\n      var _error$response4;\n      console.error('API Error:', ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.data) || error.message);\n    }\n  }\n\n  // Token management\n  getAccessToken() {\n    return localStorage.getItem('access_token');\n  }\n  getRefreshToken() {\n    return localStorage.getItem('refresh_token');\n  }\n  setTokens(accessToken, refreshToken) {\n    localStorage.setItem('access_token', accessToken);\n    localStorage.setItem('refresh_token', refreshToken);\n  }\n  clearTokens() {\n    localStorage.removeItem('access_token');\n    localStorage.removeItem('refresh_token');\n  }\n  async refreshAccessToken(refreshToken) {\n    return this.api.post('/auth/token/refresh/', {\n      refresh: refreshToken\n    });\n  }\n\n  // Authentication endpoints\n  async login(credentials) {\n    const response = await this.api.post('/auth/login/', credentials);\n    if (response.data.access_token) {\n      this.setTokens(response.data.access_token, response.data.refresh_token);\n    }\n    return response.data;\n  }\n  async register(data) {\n    const response = await this.api.post('/auth/register/', data);\n    return response.data;\n  }\n  async logout() {\n    const refreshToken = this.getRefreshToken();\n    try {\n      await this.api.post('/auth/logout/', {\n        refresh_token: refreshToken\n      });\n    } finally {\n      this.clearTokens();\n    }\n  }\n  async getCurrentUser() {\n    const response = await this.api.get('/auth/profile/');\n    return response.data;\n  }\n  async updateProfile(data) {\n    const response = await this.api.put('/auth/profile/', data);\n    return response.data;\n  }\n  async changePassword(data) {\n    const response = await this.api.post('/auth/change-password/', data);\n    return response.data;\n  }\n\n  // Portfolio endpoints\n  async getPortfolios() {\n    const response = await this.api.get('/portfolio/');\n    return response.data;\n  }\n  async getPortfolio(id) {\n    const response = await this.api.get(`/portfolio/${id}/`);\n    return response.data;\n  }\n  async createPortfolio(data) {\n    const response = await this.api.post('/portfolio/', data);\n    return response.data;\n  }\n  async updatePortfolio(id, data) {\n    const response = await this.api.put(`/portfolio/${id}/`, data);\n    return response.data;\n  }\n  async deletePortfolio(id) {\n    await this.api.delete(`/portfolio/${id}/`);\n  }\n\n  // Asset endpoints\n  async getAssets(params) {\n    const response = await this.api.get('/portfolio/assets/', {\n      params\n    });\n    return response.data;\n  }\n\n  // Holdings endpoints\n  async getHoldings(portfolioId) {\n    const response = await this.api.get(`/portfolio/${portfolioId}/holdings/`);\n    return response.data;\n  }\n  async createHolding(portfolioId, data) {\n    const response = await this.api.post(`/portfolio/${portfolioId}/holdings/`, data);\n    return response.data;\n  }\n\n  // Transaction endpoints\n  async getTransactions(portfolioId) {\n    const response = await this.api.get(`/portfolio/${portfolioId}/transactions/`);\n    return response.data;\n  }\n  async createTransaction(portfolioId, data) {\n    const response = await this.api.post(`/portfolio/${portfolioId}/transactions/`, data);\n    return response.data;\n  }\n\n  // Analytics endpoints\n  async getPortfolioAnalytics(portfolioId) {\n    const response = await this.api.get(`/portfolio/${portfolioId}/analytics/`);\n    return response.data;\n  }\n\n  // Security endpoints\n  async getSecurityDashboard() {\n    const response = await this.api.get('/security/dashboard/');\n    return response.data;\n  }\n  async getSecurityEvents(params) {\n    const response = await this.api.get('/security/events/', {\n      params\n    });\n    return response.data;\n  }\n  async getAuditLogs(params) {\n    const response = await this.api.get('/security/audit-logs/', {\n      params\n    });\n    return response.data;\n  }\n\n  // Health check\n  async healthCheck() {\n    const response = await this.api.get('/core/status/');\n    return response.data;\n  }\n\n  // Generic request method\n  async request(config) {\n    const response = await this.api.request(config);\n    return response.data;\n  }\n}\n\n// Create singleton instance\nconst apiService = new ApiService();\nexport default apiService;", "map": {"version": 3, "names": ["axios", "toast", "ApiService", "constructor", "api", "baseURL", "process", "env", "REACT_APP_API_URL", "create", "timeout", "headers", "setupInterceptors", "interceptors", "request", "use", "config", "token", "getAccessToken", "Authorization", "NODE_ENV", "_config$method", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "_error$response", "originalRequest", "status", "_retry", "refreshToken", "getRefreshToken", "refreshAccessToken", "setTokens", "data", "access_token", "refreshError", "clearTokens", "window", "location", "href", "handleApiError", "_error$response2", "_error$response2$data", "_error$response3", "message", "silentErrors", "includes", "_error$response4", "localStorage", "getItem", "accessToken", "setItem", "removeItem", "post", "refresh", "login", "credentials", "refresh_token", "register", "logout", "getCurrentUser", "get", "updateProfile", "put", "changePassword", "getPortfolios", "getPortfolio", "id", "createPortfolio", "updatePortfolio", "deletePortfolio", "delete", "getAssets", "params", "getHoldings", "portfolioId", "createHolding", "getTransactions", "createTransaction", "getPortfolioAnalytics", "getSecurityDashboard", "getSecurityEvents", "getAuditLogs", "healthCheck", "apiService"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/services/api.ts"], "sourcesContent": ["// TrustVault - API Service\n\nimport axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport { toast } from 'react-hot-toast';\n\n// Types\nimport {\n  AuthResponse,\n  LoginCredentials,\n  RegisterData,\n  User,\n  Portfolio,\n  Asset,\n  Transaction,\n  SecurityDashboard,\n  ChangePasswordData,\n  ApiError,\n  PaginatedResponse,\n} from '../types';\n\nclass ApiService {\n  private api: AxiosInstance;\n  private baseURL: string;\n\n  constructor() {\n    this.baseURL = process.env.REACT_APP_API_URL || '/api/v1';\n    \n    this.api = axios.create({\n      baseURL: this.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors(): void {\n    // Request interceptor\n    this.api.interceptors.request.use(\n      (config) => {\n        // Add auth token if available\n        const token = this.getAccessToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n\n        // Add security headers\n        config.headers['X-Requested-With'] = 'XMLHttpRequest';\n        config.headers['X-Client-Version'] = '1.0.0';\n\n        // Log request in development\n        if (process.env.NODE_ENV === 'development') {\n          console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\n        }\n\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor\n    this.api.interceptors.response.use(\n      (response: AxiosResponse) => {\n        return response;\n      },\n      async (error) => {\n        const originalRequest = error.config;\n\n        // Handle 401 errors (unauthorized)\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true;\n\n          try {\n            // Try to refresh token\n            const refreshToken = this.getRefreshToken();\n            if (refreshToken) {\n              const response = await this.refreshAccessToken(refreshToken);\n              this.setTokens(response.data.access_token, refreshToken);\n              \n              // Retry original request\n              originalRequest.headers.Authorization = `Bearer ${response.data.access_token}`;\n              return this.api(originalRequest);\n            }\n          } catch (refreshError) {\n            // Refresh failed, redirect to login\n            this.clearTokens();\n            window.location.href = '/login';\n            return Promise.reject(refreshError);\n          }\n        }\n\n        // Handle other errors\n        this.handleApiError(error);\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  private handleApiError(error: any): void {\n    const message = error.response?.data?.message || error.message || 'An error occurred';\n    \n    // Don't show toast for certain errors\n    const silentErrors = [401, 403];\n    if (!silentErrors.includes(error.response?.status)) {\n      toast.error(message);\n    }\n\n    // Log error in development\n    if (process.env.NODE_ENV === 'development') {\n      console.error('API Error:', error.response?.data || error.message);\n    }\n  }\n\n  // Token management\n  private getAccessToken(): string | null {\n    return localStorage.getItem('access_token');\n  }\n\n  private getRefreshToken(): string | null {\n    return localStorage.getItem('refresh_token');\n  }\n\n  private setTokens(accessToken: string, refreshToken: string): void {\n    localStorage.setItem('access_token', accessToken);\n    localStorage.setItem('refresh_token', refreshToken);\n  }\n\n  private clearTokens(): void {\n    localStorage.removeItem('access_token');\n    localStorage.removeItem('refresh_token');\n  }\n\n  private async refreshAccessToken(refreshToken: string): Promise<AxiosResponse> {\n    return this.api.post('/auth/token/refresh/', {\n      refresh: refreshToken,\n    });\n  }\n\n  // Authentication endpoints\n  async login(credentials: LoginCredentials): Promise<AuthResponse> {\n    const response = await this.api.post<AuthResponse>('/auth/login/', credentials);\n    \n    if (response.data.access_token) {\n      this.setTokens(response.data.access_token, response.data.refresh_token);\n    }\n    \n    return response.data;\n  }\n\n  async register(data: RegisterData): Promise<{ message: string; user_id: string }> {\n    const response = await this.api.post('/auth/register/', data);\n    return response.data;\n  }\n\n  async logout(): Promise<void> {\n    const refreshToken = this.getRefreshToken();\n    \n    try {\n      await this.api.post('/auth/logout/', {\n        refresh_token: refreshToken,\n      });\n    } finally {\n      this.clearTokens();\n    }\n  }\n\n  async getCurrentUser(): Promise<User> {\n    const response = await this.api.get<User>('/auth/profile/');\n    return response.data;\n  }\n\n  async updateProfile(data: Partial<User>): Promise<User> {\n    const response = await this.api.put<User>('/auth/profile/', data);\n    return response.data;\n  }\n\n  async changePassword(data: ChangePasswordData): Promise<{ message: string }> {\n    const response = await this.api.post('/auth/change-password/', data);\n    return response.data;\n  }\n\n  // Portfolio endpoints\n  async getPortfolios(): Promise<Portfolio[]> {\n    const response = await this.api.get<Portfolio[]>('/portfolio/');\n    return response.data;\n  }\n\n  async getPortfolio(id: string): Promise<Portfolio> {\n    const response = await this.api.get<Portfolio>(`/portfolio/${id}/`);\n    return response.data;\n  }\n\n  async createPortfolio(data: Partial<Portfolio>): Promise<Portfolio> {\n    const response = await this.api.post<Portfolio>('/portfolio/', data);\n    return response.data;\n  }\n\n  async updatePortfolio(id: string, data: Partial<Portfolio>): Promise<Portfolio> {\n    const response = await this.api.put<Portfolio>(`/portfolio/${id}/`, data);\n    return response.data;\n  }\n\n  async deletePortfolio(id: string): Promise<void> {\n    await this.api.delete(`/portfolio/${id}/`);\n  }\n\n  // Asset endpoints\n  async getAssets(params?: {\n    type?: string;\n    search?: string;\n    sector?: string;\n  }): Promise<Asset[]> {\n    const response = await this.api.get<Asset[]>('/portfolio/assets/', { params });\n    return response.data;\n  }\n\n  // Holdings endpoints\n  async getHoldings(portfolioId: string): Promise<any[]> {\n    const response = await this.api.get<any[]>(`/portfolio/${portfolioId}/holdings/`);\n    return response.data;\n  }\n\n  async createHolding(portfolioId: string, data: any): Promise<any> {\n    const response = await this.api.post<any>(`/portfolio/${portfolioId}/holdings/`, data);\n    return response.data;\n  }\n\n  // Transaction endpoints\n  async getTransactions(portfolioId: string): Promise<Transaction[]> {\n    const response = await this.api.get<Transaction[]>(`/portfolio/${portfolioId}/transactions/`);\n    return response.data;\n  }\n\n  async createTransaction(portfolioId: string, data: Partial<Transaction>): Promise<Transaction> {\n    const response = await this.api.post<Transaction>(`/portfolio/${portfolioId}/transactions/`, data);\n    return response.data;\n  }\n\n  // Analytics endpoints\n  async getPortfolioAnalytics(portfolioId: string): Promise<any> {\n    const response = await this.api.get(`/portfolio/${portfolioId}/analytics/`);\n    return response.data;\n  }\n\n  // Security endpoints\n  async getSecurityDashboard(): Promise<SecurityDashboard> {\n    const response = await this.api.get<SecurityDashboard>('/security/dashboard/');\n    return response.data;\n  }\n\n  async getSecurityEvents(params?: {\n    risk_level?: string;\n    event_type?: string;\n    is_resolved?: boolean;\n    page?: number;\n  }): Promise<PaginatedResponse<any>> {\n    const response = await this.api.get('/security/events/', { params });\n    return response.data;\n  }\n\n  async getAuditLogs(params?: {\n    action?: string;\n    resource_type?: string;\n    user_id?: string;\n    severity?: string;\n    page?: number;\n  }): Promise<PaginatedResponse<any>> {\n    const response = await this.api.get('/security/audit-logs/', { params });\n    return response.data;\n  }\n\n  // Health check\n  async healthCheck(): Promise<{ status: string }> {\n    const response = await this.api.get('/core/status/');\n    return response.data;\n  }\n\n  // Generic request method\n  async request<T>(config: AxiosRequestConfig): Promise<T> {\n    const response = await this.api.request<T>(config);\n    return response.data;\n  }\n}\n\n// Create singleton instance\nconst apiService = new ApiService();\n\nexport default apiService;\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAK,MAA4D,OAAO;AAC/E,SAASC,KAAK,QAAQ,iBAAiB;;AAEvC;;AAeA,MAAMC,UAAU,CAAC;EAIfC,WAAWA,CAAA,EAAG;IAAA,KAHNC,GAAG;IAAA,KACHC,OAAO;IAGb,IAAI,CAACA,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,SAAS;IAEzD,IAAI,CAACJ,GAAG,GAAGJ,KAAK,CAACS,MAAM,CAAC;MACtBJ,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBK,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;EAEQA,iBAAiBA,CAAA,EAAS;IAChC;IACA,IAAI,CAACR,GAAG,CAACS,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9BC,MAAM,IAAK;MACV;MACA,MAAMC,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACnC,IAAID,KAAK,EAAE;QACTD,MAAM,CAACL,OAAO,CAACQ,aAAa,GAAG,UAAUF,KAAK,EAAE;MAClD;;MAEA;MACAD,MAAM,CAACL,OAAO,CAAC,kBAAkB,CAAC,GAAG,gBAAgB;MACrDK,MAAM,CAACL,OAAO,CAAC,kBAAkB,CAAC,GAAG,OAAO;;MAE5C;MACA,IAAIL,OAAO,CAACC,GAAG,CAACa,QAAQ,KAAK,aAAa,EAAE;QAAA,IAAAC,cAAA;QAC1CC,OAAO,CAACC,GAAG,CAAC,iBAAAF,cAAA,GAAgBL,MAAM,CAACQ,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,IAAIT,MAAM,CAACU,GAAG,EAAE,CAAC;MAC3E;MAEA,OAAOV,MAAM;IACf,CAAC,EACAW,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,IAAI,CAACvB,GAAG,CAACS,YAAY,CAACiB,QAAQ,CAACf,GAAG,CAC/Be,QAAuB,IAAK;MAC3B,OAAOA,QAAQ;IACjB,CAAC,EACD,MAAOH,KAAK,IAAK;MAAA,IAAAI,eAAA;MACf,MAAMC,eAAe,GAAGL,KAAK,CAACX,MAAM;;MAEpC;MACA,IAAI,EAAAe,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAI,CAACD,eAAe,CAACE,MAAM,EAAE;QAC7DF,eAAe,CAACE,MAAM,GAAG,IAAI;QAE7B,IAAI;UACF;UACA,MAAMC,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;UAC3C,IAAID,YAAY,EAAE;YAChB,MAAML,QAAQ,GAAG,MAAM,IAAI,CAACO,kBAAkB,CAACF,YAAY,CAAC;YAC5D,IAAI,CAACG,SAAS,CAACR,QAAQ,CAACS,IAAI,CAACC,YAAY,EAAEL,YAAY,CAAC;;YAExD;YACAH,eAAe,CAACrB,OAAO,CAACQ,aAAa,GAAG,UAAUW,QAAQ,CAACS,IAAI,CAACC,YAAY,EAAE;YAC9E,OAAO,IAAI,CAACpC,GAAG,CAAC4B,eAAe,CAAC;UAClC;QACF,CAAC,CAAC,OAAOS,YAAY,EAAE;UACrB;UACA,IAAI,CAACC,WAAW,CAAC,CAAC;UAClBC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;UAC/B,OAAOjB,OAAO,CAACC,MAAM,CAACY,YAAY,CAAC;QACrC;MACF;;MAEA;MACA,IAAI,CAACK,cAAc,CAACnB,KAAK,CAAC;MAC1B,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;EAEQmB,cAAcA,CAACnB,KAAU,EAAQ;IAAA,IAAAoB,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA;IACvC,MAAMC,OAAO,GAAG,EAAAH,gBAAA,GAAApB,KAAK,CAACG,QAAQ,cAAAiB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBR,IAAI,cAAAS,qBAAA,uBAApBA,qBAAA,CAAsBE,OAAO,KAAIvB,KAAK,CAACuB,OAAO,IAAI,mBAAmB;;IAErF;IACA,MAAMC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IAC/B,IAAI,CAACA,YAAY,CAACC,QAAQ,EAAAH,gBAAA,GAACtB,KAAK,CAACG,QAAQ,cAAAmB,gBAAA,uBAAdA,gBAAA,CAAgBhB,MAAM,CAAC,EAAE;MAClDhC,KAAK,CAAC0B,KAAK,CAACuB,OAAO,CAAC;IACtB;;IAEA;IACA,IAAI5C,OAAO,CAACC,GAAG,CAACa,QAAQ,KAAK,aAAa,EAAE;MAAA,IAAAiC,gBAAA;MAC1C/B,OAAO,CAACK,KAAK,CAAC,YAAY,EAAE,EAAA0B,gBAAA,GAAA1B,KAAK,CAACG,QAAQ,cAAAuB,gBAAA,uBAAdA,gBAAA,CAAgBd,IAAI,KAAIZ,KAAK,CAACuB,OAAO,CAAC;IACpE;EACF;;EAEA;EACQhC,cAAcA,CAAA,EAAkB;IACtC,OAAOoC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAC7C;EAEQnB,eAAeA,CAAA,EAAkB;IACvC,OAAOkB,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;EAC9C;EAEQjB,SAASA,CAACkB,WAAmB,EAAErB,YAAoB,EAAQ;IACjEmB,YAAY,CAACG,OAAO,CAAC,cAAc,EAAED,WAAW,CAAC;IACjDF,YAAY,CAACG,OAAO,CAAC,eAAe,EAAEtB,YAAY,CAAC;EACrD;EAEQO,WAAWA,CAAA,EAAS;IAC1BY,YAAY,CAACI,UAAU,CAAC,cAAc,CAAC;IACvCJ,YAAY,CAACI,UAAU,CAAC,eAAe,CAAC;EAC1C;EAEA,MAAcrB,kBAAkBA,CAACF,YAAoB,EAA0B;IAC7E,OAAO,IAAI,CAAC/B,GAAG,CAACuD,IAAI,CAAC,sBAAsB,EAAE;MAC3CC,OAAO,EAAEzB;IACX,CAAC,CAAC;EACJ;;EAEA;EACA,MAAM0B,KAAKA,CAACC,WAA6B,EAAyB;IAChE,MAAMhC,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAACuD,IAAI,CAAe,cAAc,EAAEG,WAAW,CAAC;IAE/E,IAAIhC,QAAQ,CAACS,IAAI,CAACC,YAAY,EAAE;MAC9B,IAAI,CAACF,SAAS,CAACR,QAAQ,CAACS,IAAI,CAACC,YAAY,EAAEV,QAAQ,CAACS,IAAI,CAACwB,aAAa,CAAC;IACzE;IAEA,OAAOjC,QAAQ,CAACS,IAAI;EACtB;EAEA,MAAMyB,QAAQA,CAACzB,IAAkB,EAAiD;IAChF,MAAMT,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAACuD,IAAI,CAAC,iBAAiB,EAAEpB,IAAI,CAAC;IAC7D,OAAOT,QAAQ,CAACS,IAAI;EACtB;EAEA,MAAM0B,MAAMA,CAAA,EAAkB;IAC5B,MAAM9B,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IAE3C,IAAI;MACF,MAAM,IAAI,CAAChC,GAAG,CAACuD,IAAI,CAAC,eAAe,EAAE;QACnCI,aAAa,EAAE5B;MACjB,CAAC,CAAC;IACJ,CAAC,SAAS;MACR,IAAI,CAACO,WAAW,CAAC,CAAC;IACpB;EACF;EAEA,MAAMwB,cAAcA,CAAA,EAAkB;IACpC,MAAMpC,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAAC+D,GAAG,CAAO,gBAAgB,CAAC;IAC3D,OAAOrC,QAAQ,CAACS,IAAI;EACtB;EAEA,MAAM6B,aAAaA,CAAC7B,IAAmB,EAAiB;IACtD,MAAMT,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAACiE,GAAG,CAAO,gBAAgB,EAAE9B,IAAI,CAAC;IACjE,OAAOT,QAAQ,CAACS,IAAI;EACtB;EAEA,MAAM+B,cAAcA,CAAC/B,IAAwB,EAAgC;IAC3E,MAAMT,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAACuD,IAAI,CAAC,wBAAwB,EAAEpB,IAAI,CAAC;IACpE,OAAOT,QAAQ,CAACS,IAAI;EACtB;;EAEA;EACA,MAAMgC,aAAaA,CAAA,EAAyB;IAC1C,MAAMzC,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAAC+D,GAAG,CAAc,aAAa,CAAC;IAC/D,OAAOrC,QAAQ,CAACS,IAAI;EACtB;EAEA,MAAMiC,YAAYA,CAACC,EAAU,EAAsB;IACjD,MAAM3C,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAAC+D,GAAG,CAAY,cAAcM,EAAE,GAAG,CAAC;IACnE,OAAO3C,QAAQ,CAACS,IAAI;EACtB;EAEA,MAAMmC,eAAeA,CAACnC,IAAwB,EAAsB;IAClE,MAAMT,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAACuD,IAAI,CAAY,aAAa,EAAEpB,IAAI,CAAC;IACpE,OAAOT,QAAQ,CAACS,IAAI;EACtB;EAEA,MAAMoC,eAAeA,CAACF,EAAU,EAAElC,IAAwB,EAAsB;IAC9E,MAAMT,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAACiE,GAAG,CAAY,cAAcI,EAAE,GAAG,EAAElC,IAAI,CAAC;IACzE,OAAOT,QAAQ,CAACS,IAAI;EACtB;EAEA,MAAMqC,eAAeA,CAACH,EAAU,EAAiB;IAC/C,MAAM,IAAI,CAACrE,GAAG,CAACyE,MAAM,CAAC,cAAcJ,EAAE,GAAG,CAAC;EAC5C;;EAEA;EACA,MAAMK,SAASA,CAACC,MAIf,EAAoB;IACnB,MAAMjD,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAAC+D,GAAG,CAAU,oBAAoB,EAAE;MAAEY;IAAO,CAAC,CAAC;IAC9E,OAAOjD,QAAQ,CAACS,IAAI;EACtB;;EAEA;EACA,MAAMyC,WAAWA,CAACC,WAAmB,EAAkB;IACrD,MAAMnD,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAAC+D,GAAG,CAAQ,cAAcc,WAAW,YAAY,CAAC;IACjF,OAAOnD,QAAQ,CAACS,IAAI;EACtB;EAEA,MAAM2C,aAAaA,CAACD,WAAmB,EAAE1C,IAAS,EAAgB;IAChE,MAAMT,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAACuD,IAAI,CAAM,cAAcsB,WAAW,YAAY,EAAE1C,IAAI,CAAC;IACtF,OAAOT,QAAQ,CAACS,IAAI;EACtB;;EAEA;EACA,MAAM4C,eAAeA,CAACF,WAAmB,EAA0B;IACjE,MAAMnD,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAAC+D,GAAG,CAAgB,cAAcc,WAAW,gBAAgB,CAAC;IAC7F,OAAOnD,QAAQ,CAACS,IAAI;EACtB;EAEA,MAAM6C,iBAAiBA,CAACH,WAAmB,EAAE1C,IAA0B,EAAwB;IAC7F,MAAMT,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAACuD,IAAI,CAAc,cAAcsB,WAAW,gBAAgB,EAAE1C,IAAI,CAAC;IAClG,OAAOT,QAAQ,CAACS,IAAI;EACtB;;EAEA;EACA,MAAM8C,qBAAqBA,CAACJ,WAAmB,EAAgB;IAC7D,MAAMnD,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAAC+D,GAAG,CAAC,cAAcc,WAAW,aAAa,CAAC;IAC3E,OAAOnD,QAAQ,CAACS,IAAI;EACtB;;EAEA;EACA,MAAM+C,oBAAoBA,CAAA,EAA+B;IACvD,MAAMxD,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAAC+D,GAAG,CAAoB,sBAAsB,CAAC;IAC9E,OAAOrC,QAAQ,CAACS,IAAI;EACtB;EAEA,MAAMgD,iBAAiBA,CAACR,MAKvB,EAAmC;IAClC,MAAMjD,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAAC+D,GAAG,CAAC,mBAAmB,EAAE;MAAEY;IAAO,CAAC,CAAC;IACpE,OAAOjD,QAAQ,CAACS,IAAI;EACtB;EAEA,MAAMiD,YAAYA,CAACT,MAMlB,EAAmC;IAClC,MAAMjD,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAAC+D,GAAG,CAAC,uBAAuB,EAAE;MAAEY;IAAO,CAAC,CAAC;IACxE,OAAOjD,QAAQ,CAACS,IAAI;EACtB;;EAEA;EACA,MAAMkD,WAAWA,CAAA,EAAgC;IAC/C,MAAM3D,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAAC+D,GAAG,CAAC,eAAe,CAAC;IACpD,OAAOrC,QAAQ,CAACS,IAAI;EACtB;;EAEA;EACA,MAAMzB,OAAOA,CAAIE,MAA0B,EAAc;IACvD,MAAMc,QAAQ,GAAG,MAAM,IAAI,CAAC1B,GAAG,CAACU,OAAO,CAAIE,MAAM,CAAC;IAClD,OAAOc,QAAQ,CAACS,IAAI;EACtB;AACF;;AAEA;AACA,MAAMmD,UAAU,GAAG,IAAIxF,UAAU,CAAC,CAAC;AAEnC,eAAewF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}