{"ast": null, "code": "// TrustVault - ErrorBoundary exports\n\nexport { default, withErrorBoundary, useErrorHandler } from './ErrorBoundary';", "map": {"version": 3, "names": ["default", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useErrorHandler"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/components/ErrorBoundary/index.ts"], "sourcesContent": ["// TrustVault - ErrorBoundary exports\n\nexport { default, withErrorBoundary, useErrorHandler } from './ErrorBoundary';\n"], "mappings": "AAAA;;AAEA,SAASA,OAAO,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}