{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\pages\\\\Portfolio\\\\AddHoldingPage.tsx\",\n  _s = $RefreshSig$();\n// TrustVault - Add Holding Page\n\nimport React, { useState, createElement as _createElement } from 'react';\nimport { Box, Typography, Paper, TextField, Button, Autocomplete, Alert, CircularProgress, Grid, InputAdornment } from '@mui/material';\nimport { ArrowBack, Add } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useMutation, useQuery, useQueryClient } from 'react-query';\nimport { toast } from 'react-hot-toast';\n\n// Services\nimport apiService from '../../services/api';\n\n// Types\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AddHoldingPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    id: portfolioId\n  } = useParams();\n  const queryClient = useQueryClient();\n  const [formData, setFormData] = useState({\n    asset_id: '',\n    quantity: 0,\n    purchase_price: 0,\n    purchase_date: new Date().toISOString().split('T')[0],\n    notes: ''\n  });\n  const [selectedAsset, setSelectedAsset] = useState(null);\n  const [errors, setErrors] = useState({});\n\n  // Fetch available assets\n  const {\n    data: assets,\n    isLoading: assetsLoading,\n    error: assetsError\n  } = useQuery('assets', () => apiService.getAssets(), {\n    retry: 1,\n    staleTime: 5 * 60 * 1000,\n    // 5 minutes\n    onError: error => {\n      console.error('Failed to fetch assets:', error);\n    },\n    // Provide fallback data structure\n    placeholderData: []\n  });\n\n  // Ensure assets is always an array\n  const assetOptions = React.useMemo(() => {\n    // If there's an error or no assets, provide some sample data for testing\n    if (assetsError || !assets || !Array.isArray(assets) || assets.length === 0) {\n      return [{\n        id: 'sample-1',\n        symbol: 'AAPL',\n        name: 'Apple Inc.',\n        asset_type: 'STOCK',\n        sector: 'Technology',\n        current_price: '175.50',\n        currency: 'USD',\n        description: 'Apple Inc. - Sample Data',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }, {\n        id: 'sample-2',\n        symbol: 'GOOGL',\n        name: 'Alphabet Inc.',\n        asset_type: 'STOCK',\n        sector: 'Technology',\n        current_price: '142.80',\n        currency: 'USD',\n        description: 'Alphabet Inc. - Sample Data',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }, {\n        id: 'sample-3',\n        symbol: 'MSFT',\n        name: 'Microsoft Corporation',\n        asset_type: 'STOCK',\n        sector: 'Technology',\n        current_price: '420.15',\n        currency: 'USD',\n        description: 'Microsoft Corporation - Sample Data',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }, {\n        id: 'sample-4',\n        symbol: 'TSLA',\n        name: 'Tesla Inc.',\n        asset_type: 'STOCK',\n        sector: 'Consumer Discretionary',\n        current_price: '248.50',\n        currency: 'USD',\n        description: 'Tesla Inc. - Sample Data',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }, {\n        id: 'sample-5',\n        symbol: 'AMZN',\n        name: 'Amazon.com Inc.',\n        asset_type: 'STOCK',\n        sector: 'Consumer Discretionary',\n        current_price: '185.25',\n        currency: 'USD',\n        description: 'Amazon.com Inc. - Sample Data',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }];\n    }\n    return assets;\n  }, [assets, assetsError]);\n\n  // Fetch portfolio details\n  const {\n    data: portfolio\n  } = useQuery(['portfolio', portfolioId], () => apiService.getPortfolio(portfolioId), {\n    enabled: !!portfolioId\n  });\n  const addHoldingMutation = useMutation(data => {\n    if (!portfolioId) throw new Error('Portfolio ID is required');\n    return apiService.createHolding(portfolioId, data);\n  }, {\n    onSuccess: () => {\n      toast.success('Holding added successfully!');\n      queryClient.invalidateQueries(['portfolio', portfolioId]);\n      queryClient.invalidateQueries(['holdings', portfolioId]);\n      navigate(`/portfolios/${portfolioId}`);\n    },\n    onError: error => {\n      var _error$response;\n      console.error('Add holding error:', error);\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && _error$response.data) {\n        setErrors(error.response.data);\n      } else {\n        toast.error('Failed to add holding. Please try again.');\n      }\n    }\n  });\n  const handleInputChange = field => event => {\n    const value = event.target.type === 'number' ? parseFloat(event.target.value) || 0 : event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear error when user starts typing\n    if (field in errors) {\n      setErrors(prev => {\n        const newErrors = {\n          ...prev\n        };\n        delete newErrors[field];\n        return newErrors;\n      });\n    }\n  };\n  const handleAssetChange = (event, newValue) => {\n    setSelectedAsset(newValue);\n    setFormData(prev => ({\n      ...prev,\n      asset_id: (newValue === null || newValue === void 0 ? void 0 : newValue.id) || ''\n    }));\n\n    // Clear asset_id error when user selects an asset\n    if (errors.asset_id) {\n      setErrors(prev => {\n        const newErrors = {\n          ...prev\n        };\n        delete newErrors.asset_id;\n        return newErrors;\n      });\n    }\n  };\n  const handleSubmit = event => {\n    event.preventDefault();\n\n    // Basic validation\n    const newErrors = {};\n    if (!formData.asset_id) {\n      newErrors.asset_id = 'Please select an asset';\n    }\n    if (formData.quantity <= 0) {\n      newErrors.quantity = 'Quantity must be greater than 0';\n    }\n    if (formData.purchase_price <= 0) {\n      newErrors.purchase_price = 'Purchase price must be greater than 0';\n    }\n    if (!formData.purchase_date) {\n      newErrors.purchase_date = 'Purchase date is required';\n    }\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    addHoldingMutation.mutate(formData);\n  };\n  const calculateTotalValue = () => {\n    return formData.quantity * formData.purchase_price;\n  };\n  if (!portfolioId) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: 800,\n        mx: 'auto',\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"error\",\n        children: \"Portfolio ID is required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Add Holding - TrustVault\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Add a new holding to your portfolio\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: 800,\n        mx: 'auto',\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 24\n          }, this),\n          onClick: () => navigate(`/portfolios/${portfolioId}`),\n          sx: {\n            mr: 2\n          },\n          children: \"Back to Portfolio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            component: \"h1\",\n            children: \"Add New Holding\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), portfolio && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"to \", portfolio.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: assetsError && !assetOptions.length ? /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"warning\",\n                sx: {\n                  mb: 2\n                },\n                children: [\"Unable to load assets from server. Using sample data for testing.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"Note: Please ensure the backend server is running for full functionality.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this) : !assetsLoading && assetOptions.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"warning\",\n                sx: {\n                  mb: 2\n                },\n                children: \"No assets available. Please contact your administrator to add assets to the system.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Autocomplete, {\n                options: assetOptions,\n                getOptionLabel: option => {\n                  if (!option || typeof option !== 'object') return '';\n                  return `${option.symbol || 'N/A'} - ${option.name || 'Unknown Asset'}`;\n                },\n                value: selectedAsset,\n                onChange: handleAssetChange,\n                loading: assetsLoading,\n                disabled: assetsLoading || !!assetsError || assetOptions.length === 0,\n                isOptionEqualToValue: (option, value) => {\n                  if (!option || !value) return false;\n                  return option.id === value.id;\n                },\n                noOptionsText: assetsLoading ? \"Loading...\" : \"No assets found\",\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  label: \"Select Asset\",\n                  error: !!errors.asset_id,\n                  helperText: errors.asset_id || (assetsLoading ? 'Loading assets...' : assetOptions.length === 0 ? 'No assets available' : `${assetOptions.length} assets available`),\n                  required: true,\n                  InputProps: {\n                    ...params.InputProps,\n                    endAdornment: /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [assetsLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                        color: \"inherit\",\n                        size: 20\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 329,\n                        columnNumber: 48\n                      }, this) : null, params.InputProps.endAdornment]\n                    }, void 0, true)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 23\n                }, this),\n                renderOption: (props, option) => /*#__PURE__*/_createElement(Box, {\n                  component: \"li\",\n                  ...props,\n                  key: option.id || Math.random(),\n                  __self: this,\n                  __source: {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 23\n                  }\n                }, /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body1\",\n                    children: [option.symbol || 'N/A', \" - \", option.name || 'Unknown Asset']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [option.asset_type || 'Unknown', \" \\u2022 \", option.sector || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Quantity\",\n                type: \"number\",\n                value: formData.quantity || '',\n                onChange: handleInputChange('quantity'),\n                error: !!errors.quantity,\n                helperText: errors.quantity,\n                required: true,\n                fullWidth: true,\n                inputProps: {\n                  min: 0,\n                  step: 0.01\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Purchase Price\",\n                type: \"number\",\n                value: formData.purchase_price || '',\n                onChange: handleInputChange('purchase_price'),\n                error: !!errors.purchase_price,\n                helperText: errors.purchase_price,\n                required: true,\n                fullWidth: true,\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: \"$\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 37\n                  }, this)\n                },\n                inputProps: {\n                  min: 0,\n                  step: 0.01\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Purchase Date\",\n                type: \"date\",\n                value: formData.purchase_date,\n                onChange: handleInputChange('purchase_date'),\n                error: !!errors.purchase_date,\n                helperText: errors.purchase_date,\n                required: true,\n                fullWidth: true,\n                InputLabelProps: {\n                  shrink: true\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Total Value\",\n                value: `$${calculateTotalValue().toFixed(2)}`,\n                fullWidth: true,\n                disabled: true,\n                InputProps: {\n                  readOnly: true\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Notes (Optional)\",\n                value: formData.notes,\n                onChange: handleInputChange('notes'),\n                multiline: true,\n                rows: 3,\n                fullWidth: true,\n                placeholder: \"Add any notes about this holding...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), Object.keys(errors).length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"error\",\n                children: \"Please fix the errors above and try again.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                justifyContent: \"flex-end\",\n                gap: 2,\n                mt: 2,\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  onClick: () => navigate(`/portfolios/${portfolioId}`),\n                  disabled: addHoldingMutation.isLoading,\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  variant: \"contained\",\n                  startIcon: addHoldingMutation.isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 63\n                  }, this) : /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 96\n                  }, this),\n                  disabled: addHoldingMutation.isLoading,\n                  children: addHoldingMutation.isLoading ? 'Adding...' : 'Add Holding'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mt: 3,\n          bgcolor: 'background.default'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"About Holdings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Holdings represent your investments in specific assets. Once added, you can:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          component: \"ul\",\n          sx: {\n            mt: 1,\n            pl: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Track performance and current value\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Record additional transactions (buy/sell)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"View detailed analytics and charts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Monitor portfolio allocation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AddHoldingPage, \"cCPfDBcs24XM5ku4ae8aEN1DUXk=\", false, function () {\n  return [useNavigate, useParams, useQueryClient, useQuery, useQuery, useMutation];\n});\n_c = AddHoldingPage;\nexport default AddHoldingPage;\nvar _c;\n$RefreshReg$(_c, \"AddHoldingPage\");", "map": {"version": 3, "names": ["React", "useState", "createElement", "_createElement", "Box", "Typography", "Paper", "TextField", "<PERSON><PERSON>", "Autocomplete", "<PERSON><PERSON>", "CircularProgress", "Grid", "InputAdornment", "ArrowBack", "Add", "<PERSON><PERSON><PERSON>", "useNavigate", "useParams", "useMutation", "useQuery", "useQueryClient", "toast", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddHoldingPage", "_s", "navigate", "id", "portfolioId", "queryClient", "formData", "setFormData", "asset_id", "quantity", "purchase_price", "purchase_date", "Date", "toISOString", "split", "notes", "selectedAsset", "setSelectedAsset", "errors", "setErrors", "data", "assets", "isLoading", "assetsLoading", "error", "assetsError", "getAssets", "retry", "staleTime", "onError", "console", "placeholderData", "assetOptions", "useMemo", "Array", "isArray", "length", "symbol", "name", "asset_type", "sector", "current_price", "currency", "description", "created_at", "updated_at", "portfolio", "getPortfolio", "enabled", "addHoldingMutation", "Error", "createHolding", "onSuccess", "success", "invalidateQueries", "_error$response", "response", "handleInputChange", "field", "event", "value", "target", "type", "parseFloat", "prev", "newErrors", "handleAssetChange", "newValue", "handleSubmit", "preventDefault", "Object", "keys", "mutate", "calculateTotalValue", "sx", "max<PERSON><PERSON><PERSON>", "mx", "p", "children", "variant", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "display", "alignItems", "mb", "startIcon", "onClick", "mr", "component", "onSubmit", "container", "spacing", "item", "xs", "severity", "options", "getOptionLabel", "option", "onChange", "loading", "disabled", "isOptionEqualToValue", "noOptionsText", "renderInput", "params", "label", "helperText", "required", "InputProps", "endAdornment", "size", "renderOption", "props", "key", "Math", "random", "__self", "__source", "sm", "fullWidth", "inputProps", "min", "step", "startAdornment", "position", "InputLabelProps", "shrink", "toFixed", "readOnly", "multiline", "rows", "placeholder", "justifyContent", "gap", "mt", "bgcolor", "gutterBottom", "pl", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/pages/Portfolio/AddHoldingPage.tsx"], "sourcesContent": ["// TrustVault - Add Holding Page\n\nimport React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  TextField,\n  Button,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Autocomplete,\n  Alert,\n  CircularProgress,\n  Grid,\n  InputAdornment,\n} from '@mui/material';\nimport { ArrowBack, Add } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useMutation, useQuery, useQueryClient } from 'react-query';\nimport { toast } from 'react-hot-toast';\n\n// Services\nimport apiService from '../../services/api';\n\n// Types\ninterface AddHoldingForm {\n  asset_id: string;\n  quantity: number;\n  purchase_price: number;\n  purchase_date: string;\n  notes?: string;\n}\n\nconst AddHoldingPage: React.FC = () => {\n  const navigate = useNavigate();\n  const { id: portfolioId } = useParams<{ id: string }>();\n  const queryClient = useQueryClient();\n\n  const [formData, setFormData] = useState<AddHoldingForm>({\n    asset_id: '',\n    quantity: 0,\n    purchase_price: 0,\n    purchase_date: new Date().toISOString().split('T')[0],\n    notes: '',\n  });\n\n  const [selectedAsset, setSelectedAsset] = useState<any>(null);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  // Fetch available assets\n  const { data: assets, isLoading: assetsLoading, error: assetsError } = useQuery(\n    'assets',\n    () => apiService.getAssets(),\n    {\n      retry: 1,\n      staleTime: 5 * 60 * 1000, // 5 minutes\n      onError: (error) => {\n        console.error('Failed to fetch assets:', error);\n      },\n      // Provide fallback data structure\n      placeholderData: [],\n    }\n  );\n\n  // Ensure assets is always an array\n  const assetOptions = React.useMemo(() => {\n    // If there's an error or no assets, provide some sample data for testing\n    if (assetsError || !assets || !Array.isArray(assets) || assets.length === 0) {\n      return [\n        {\n          id: 'sample-1',\n          symbol: 'AAPL',\n          name: 'Apple Inc.',\n          asset_type: 'STOCK',\n          sector: 'Technology',\n          current_price: '175.50',\n          currency: 'USD',\n          description: 'Apple Inc. - Sample Data',\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        },\n        {\n          id: 'sample-2',\n          symbol: 'GOOGL',\n          name: 'Alphabet Inc.',\n          asset_type: 'STOCK',\n          sector: 'Technology',\n          current_price: '142.80',\n          currency: 'USD',\n          description: 'Alphabet Inc. - Sample Data',\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        },\n        {\n          id: 'sample-3',\n          symbol: 'MSFT',\n          name: 'Microsoft Corporation',\n          asset_type: 'STOCK',\n          sector: 'Technology',\n          current_price: '420.15',\n          currency: 'USD',\n          description: 'Microsoft Corporation - Sample Data',\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        },\n        {\n          id: 'sample-4',\n          symbol: 'TSLA',\n          name: 'Tesla Inc.',\n          asset_type: 'STOCK',\n          sector: 'Consumer Discretionary',\n          current_price: '248.50',\n          currency: 'USD',\n          description: 'Tesla Inc. - Sample Data',\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        },\n        {\n          id: 'sample-5',\n          symbol: 'AMZN',\n          name: 'Amazon.com Inc.',\n          asset_type: 'STOCK',\n          sector: 'Consumer Discretionary',\n          current_price: '185.25',\n          currency: 'USD',\n          description: 'Amazon.com Inc. - Sample Data',\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        }\n      ];\n    }\n\n    return assets;\n  }, [assets, assetsError]);\n\n  // Fetch portfolio details\n  const { data: portfolio } = useQuery(\n    ['portfolio', portfolioId],\n    () => apiService.getPortfolio(portfolioId!),\n    {\n      enabled: !!portfolioId,\n    }\n  );\n\n  const addHoldingMutation = useMutation(\n    (data: AddHoldingForm) => {\n      if (!portfolioId) throw new Error('Portfolio ID is required');\n      return apiService.createHolding(portfolioId, data);\n    },\n    {\n      onSuccess: () => {\n        toast.success('Holding added successfully!');\n        queryClient.invalidateQueries(['portfolio', portfolioId]);\n        queryClient.invalidateQueries(['holdings', portfolioId]);\n        navigate(`/portfolios/${portfolioId}`);\n      },\n      onError: (error: any) => {\n        console.error('Add holding error:', error);\n        if (error.response?.data) {\n          setErrors(error.response.data);\n        } else {\n          toast.error('Failed to add holding. Please try again.');\n        }\n      },\n    }\n  );\n\n  const handleInputChange = (field: keyof AddHoldingForm) => (\n    event: React.ChangeEvent<HTMLInputElement>\n  ) => {\n    const value = event.target.type === 'number' ? parseFloat(event.target.value) || 0 : event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value,\n    }));\n    \n    // Clear error when user starts typing\n    if (field in errors) {\n      setErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors[field];\n        return newErrors;\n      });\n    }\n  };\n\n  const handleAssetChange = (event: any, newValue: any) => {\n    setSelectedAsset(newValue);\n    setFormData(prev => ({\n      ...prev,\n      asset_id: newValue?.id || '',\n    }));\n\n    // Clear asset_id error when user selects an asset\n    if (errors.asset_id) {\n      setErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors.asset_id;\n        return newErrors;\n      });\n    }\n  };\n\n  const handleSubmit = (event: React.FormEvent) => {\n    event.preventDefault();\n    \n    // Basic validation\n    const newErrors: Record<string, string> = {};\n    \n    if (!formData.asset_id) {\n      newErrors.asset_id = 'Please select an asset';\n    }\n    \n    if (formData.quantity <= 0) {\n      newErrors.quantity = 'Quantity must be greater than 0';\n    }\n    \n    if (formData.purchase_price <= 0) {\n      newErrors.purchase_price = 'Purchase price must be greater than 0';\n    }\n    \n    if (!formData.purchase_date) {\n      newErrors.purchase_date = 'Purchase date is required';\n    }\n    \n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    \n    addHoldingMutation.mutate(formData);\n  };\n\n  const calculateTotalValue = () => {\n    return formData.quantity * formData.purchase_price;\n  };\n\n  if (!portfolioId) {\n    return (\n      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>\n        <Typography variant=\"h6\" color=\"error\">\n          Portfolio ID is required\n        </Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <>\n      <Helmet>\n        <title>Add Holding - TrustVault</title>\n        <meta name=\"description\" content=\"Add a new holding to your portfolio\" />\n      </Helmet>\n\n      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>\n        {/* Header */}\n        <Box display=\"flex\" alignItems=\"center\" mb={3}>\n          <Button\n            startIcon={<ArrowBack />}\n            onClick={() => navigate(`/portfolios/${portfolioId}`)}\n            sx={{ mr: 2 }}\n          >\n            Back to Portfolio\n          </Button>\n          <Box>\n            <Typography variant=\"h4\" component=\"h1\">\n              Add New Holding\n            </Typography>\n            {portfolio && (\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                to {portfolio.name}\n              </Typography>\n            )}\n          </Box>\n        </Box>\n\n        {/* Form */}\n        <Paper sx={{ p: 4 }}>\n          <form onSubmit={handleSubmit}>\n            <Grid container spacing={3}>\n              {/* Asset Selection */}\n              <Grid item xs={12}>\n                {assetsError && !assetOptions.length ? (\n                  <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                    Unable to load assets from server. Using sample data for testing.\n                    <br />\n                    <small>Note: Please ensure the backend server is running for full functionality.</small>\n                  </Alert>\n                ) : !assetsLoading && assetOptions.length === 0 ? (\n                  <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                    No assets available. Please contact your administrator to add assets to the system.\n                  </Alert>\n                ) : (\n                  <Autocomplete\n                    options={assetOptions}\n                    getOptionLabel={(option) => {\n                      if (!option || typeof option !== 'object') return '';\n                      return `${option.symbol || 'N/A'} - ${option.name || 'Unknown Asset'}`;\n                    }}\n                    value={selectedAsset}\n                    onChange={handleAssetChange}\n                    loading={assetsLoading}\n                    disabled={assetsLoading || !!assetsError || assetOptions.length === 0}\n                    isOptionEqualToValue={(option, value) => {\n                      if (!option || !value) return false;\n                      return option.id === value.id;\n                    }}\n                    noOptionsText={assetsLoading ? \"Loading...\" : \"No assets found\"}\n                    renderInput={(params) => (\n                      <TextField\n                        {...params}\n                        label=\"Select Asset\"\n                        error={!!errors.asset_id}\n                        helperText={\n                          errors.asset_id ||\n                          (assetsLoading ? 'Loading assets...' :\n                           assetOptions.length === 0 ? 'No assets available' :\n                           `${assetOptions.length} assets available`)\n                        }\n                        required\n                        InputProps={{\n                          ...params.InputProps,\n                          endAdornment: (\n                            <>\n                              {assetsLoading ? <CircularProgress color=\"inherit\" size={20} /> : null}\n                              {params.InputProps.endAdornment}\n                            </>\n                          ),\n                        }}\n                      />\n                    )}\n                    renderOption={(props, option) => (\n                      <Box component=\"li\" {...props} key={option.id || Math.random()}>\n                        <Box>\n                          <Typography variant=\"body1\">\n                            {option.symbol || 'N/A'} - {option.name || 'Unknown Asset'}\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            {option.asset_type || 'Unknown'} • {option.sector || 'N/A'}\n                          </Typography>\n                        </Box>\n                      </Box>\n                    )}\n                  />\n                )}\n              </Grid>\n\n              {/* Quantity */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  label=\"Quantity\"\n                  type=\"number\"\n                  value={formData.quantity || ''}\n                  onChange={handleInputChange('quantity')}\n                  error={!!errors.quantity}\n                  helperText={errors.quantity}\n                  required\n                  fullWidth\n                  inputProps={{ min: 0, step: 0.01 }}\n                />\n              </Grid>\n\n              {/* Purchase Price */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  label=\"Purchase Price\"\n                  type=\"number\"\n                  value={formData.purchase_price || ''}\n                  onChange={handleInputChange('purchase_price')}\n                  error={!!errors.purchase_price}\n                  helperText={errors.purchase_price}\n                  required\n                  fullWidth\n                  InputProps={{\n                    startAdornment: <InputAdornment position=\"start\">$</InputAdornment>,\n                  }}\n                  inputProps={{ min: 0, step: 0.01 }}\n                />\n              </Grid>\n\n              {/* Purchase Date */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  label=\"Purchase Date\"\n                  type=\"date\"\n                  value={formData.purchase_date}\n                  onChange={handleInputChange('purchase_date')}\n                  error={!!errors.purchase_date}\n                  helperText={errors.purchase_date}\n                  required\n                  fullWidth\n                  InputLabelProps={{\n                    shrink: true,\n                  }}\n                />\n              </Grid>\n\n              {/* Total Value Display */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  label=\"Total Value\"\n                  value={`$${calculateTotalValue().toFixed(2)}`}\n                  fullWidth\n                  disabled\n                  InputProps={{\n                    readOnly: true,\n                  }}\n                />\n              </Grid>\n\n              {/* Notes */}\n              <Grid item xs={12}>\n                <TextField\n                  label=\"Notes (Optional)\"\n                  value={formData.notes}\n                  onChange={handleInputChange('notes')}\n                  multiline\n                  rows={3}\n                  fullWidth\n                  placeholder=\"Add any notes about this holding...\"\n                />\n              </Grid>\n\n              {/* Error Display */}\n              {Object.keys(errors).length > 0 && (\n                <Grid item xs={12}>\n                  <Alert severity=\"error\">\n                    Please fix the errors above and try again.\n                  </Alert>\n                </Grid>\n              )}\n\n              {/* Submit Buttons */}\n              <Grid item xs={12}>\n                <Box display=\"flex\" justifyContent=\"flex-end\" gap={2} mt={2}>\n                  <Button\n                    variant=\"outlined\"\n                    onClick={() => navigate(`/portfolios/${portfolioId}`)}\n                    disabled={addHoldingMutation.isLoading}\n                  >\n                    Cancel\n                  </Button>\n                  <Button\n                    type=\"submit\"\n                    variant=\"contained\"\n                    startIcon={addHoldingMutation.isLoading ? <CircularProgress size={20} /> : <Add />}\n                    disabled={addHoldingMutation.isLoading}\n                  >\n                    {addHoldingMutation.isLoading ? 'Adding...' : 'Add Holding'}\n                  </Button>\n                </Box>\n              </Grid>\n            </Grid>\n          </form>\n        </Paper>\n\n        {/* Info Box */}\n        <Paper sx={{ p: 3, mt: 3, bgcolor: 'background.default' }}>\n          <Typography variant=\"h6\" gutterBottom>\n            About Holdings\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Holdings represent your investments in specific assets. Once added, you can:\n          </Typography>\n          <Box component=\"ul\" sx={{ mt: 1, pl: 2 }}>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Track performance and current value\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Record additional transactions (buy/sell)\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              View detailed analytics and charts\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Monitor portfolio allocation\n            </Typography>\n          </Box>\n        </Paper>\n      </Box>\n    </>\n  );\n};\n\nexport default AddHoldingPage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAAC,aAAA,IAAAC,cAAA,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,MAAM,EAKNC,YAAY,EACZC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,cAAc,QACT,eAAe;AACtB,SAASC,SAAS,EAAEC,GAAG,QAAQ,qBAAqB;AACpD,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,aAAa;AACnE,SAASC,KAAK,QAAQ,iBAAiB;;AAEvC;AACA,OAAOC,UAAU,MAAM,oBAAoB;;AAE3C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc,EAAE,EAAEC;EAAY,CAAC,GAAGd,SAAS,CAAiB,CAAC;EACvD,MAAMe,WAAW,GAAGZ,cAAc,CAAC,CAAC;EAEpC,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAiB;IACvDmC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,CAAC;IACXC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACrDC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG5C,QAAQ,CAAM,IAAI,CAAC;EAC7D,MAAM,CAAC6C,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAyB,CAAC,CAAC,CAAC;;EAEhE;EACA,MAAM;IAAE+C,IAAI,EAAEC,MAAM;IAAEC,SAAS,EAAEC,aAAa;IAAEC,KAAK,EAAEC;EAAY,CAAC,GAAGjC,QAAQ,CAC7E,QAAQ,EACR,MAAMG,UAAU,CAAC+B,SAAS,CAAC,CAAC,EAC5B;IACEC,KAAK,EAAE,CAAC;IACRC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1BC,OAAO,EAAGL,KAAK,IAAK;MAClBM,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC;IACD;IACAO,eAAe,EAAE;EACnB,CACF,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG5D,KAAK,CAAC6D,OAAO,CAAC,MAAM;IACvC;IACA,IAAIR,WAAW,IAAI,CAACJ,MAAM,IAAI,CAACa,KAAK,CAACC,OAAO,CAACd,MAAM,CAAC,IAAIA,MAAM,CAACe,MAAM,KAAK,CAAC,EAAE;MAC3E,OAAO,CACL;QACEjC,EAAE,EAAE,UAAU;QACdkC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE,OAAO;QACnBC,MAAM,EAAE,YAAY;QACpBC,aAAa,EAAE,QAAQ;QACvBC,QAAQ,EAAE,KAAK;QACfC,WAAW,EAAE,0BAA0B;QACvCC,UAAU,EAAE,IAAIhC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpCgC,UAAU,EAAE,IAAIjC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC,EACD;QACEV,EAAE,EAAE,UAAU;QACdkC,MAAM,EAAE,OAAO;QACfC,IAAI,EAAE,eAAe;QACrBC,UAAU,EAAE,OAAO;QACnBC,MAAM,EAAE,YAAY;QACpBC,aAAa,EAAE,QAAQ;QACvBC,QAAQ,EAAE,KAAK;QACfC,WAAW,EAAE,6BAA6B;QAC1CC,UAAU,EAAE,IAAIhC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpCgC,UAAU,EAAE,IAAIjC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC,EACD;QACEV,EAAE,EAAE,UAAU;QACdkC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE,uBAAuB;QAC7BC,UAAU,EAAE,OAAO;QACnBC,MAAM,EAAE,YAAY;QACpBC,aAAa,EAAE,QAAQ;QACvBC,QAAQ,EAAE,KAAK;QACfC,WAAW,EAAE,qCAAqC;QAClDC,UAAU,EAAE,IAAIhC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpCgC,UAAU,EAAE,IAAIjC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC,EACD;QACEV,EAAE,EAAE,UAAU;QACdkC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE,OAAO;QACnBC,MAAM,EAAE,wBAAwB;QAChCC,aAAa,EAAE,QAAQ;QACvBC,QAAQ,EAAE,KAAK;QACfC,WAAW,EAAE,0BAA0B;QACvCC,UAAU,EAAE,IAAIhC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpCgC,UAAU,EAAE,IAAIjC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC,EACD;QACEV,EAAE,EAAE,UAAU;QACdkC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAE,OAAO;QACnBC,MAAM,EAAE,wBAAwB;QAChCC,aAAa,EAAE,QAAQ;QACvBC,QAAQ,EAAE,KAAK;QACfC,WAAW,EAAE,+BAA+B;QAC5CC,UAAU,EAAE,IAAIhC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpCgC,UAAU,EAAE,IAAIjC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC,CACF;IACH;IAEA,OAAOQ,MAAM;EACf,CAAC,EAAE,CAACA,MAAM,EAAEI,WAAW,CAAC,CAAC;;EAEzB;EACA,MAAM;IAAEL,IAAI,EAAE0B;EAAU,CAAC,GAAGtD,QAAQ,CAClC,CAAC,WAAW,EAAEY,WAAW,CAAC,EAC1B,MAAMT,UAAU,CAACoD,YAAY,CAAC3C,WAAY,CAAC,EAC3C;IACE4C,OAAO,EAAE,CAAC,CAAC5C;EACb,CACF,CAAC;EAED,MAAM6C,kBAAkB,GAAG1D,WAAW,CACnC6B,IAAoB,IAAK;IACxB,IAAI,CAAChB,WAAW,EAAE,MAAM,IAAI8C,KAAK,CAAC,0BAA0B,CAAC;IAC7D,OAAOvD,UAAU,CAACwD,aAAa,CAAC/C,WAAW,EAAEgB,IAAI,CAAC;EACpD,CAAC,EACD;IACEgC,SAAS,EAAEA,CAAA,KAAM;MACf1D,KAAK,CAAC2D,OAAO,CAAC,6BAA6B,CAAC;MAC5ChD,WAAW,CAACiD,iBAAiB,CAAC,CAAC,WAAW,EAAElD,WAAW,CAAC,CAAC;MACzDC,WAAW,CAACiD,iBAAiB,CAAC,CAAC,UAAU,EAAElD,WAAW,CAAC,CAAC;MACxDF,QAAQ,CAAC,eAAeE,WAAW,EAAE,CAAC;IACxC,CAAC;IACDyB,OAAO,EAAGL,KAAU,IAAK;MAAA,IAAA+B,eAAA;MACvBzB,OAAO,CAACN,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,KAAA+B,eAAA,GAAI/B,KAAK,CAACgC,QAAQ,cAAAD,eAAA,eAAdA,eAAA,CAAgBnC,IAAI,EAAE;QACxBD,SAAS,CAACK,KAAK,CAACgC,QAAQ,CAACpC,IAAI,CAAC;MAChC,CAAC,MAAM;QACL1B,KAAK,CAAC8B,KAAK,CAAC,0CAA0C,CAAC;MACzD;IACF;EACF,CACF,CAAC;EAED,MAAMiC,iBAAiB,GAAIC,KAA2B,IACpDC,KAA0C,IACvC;IACH,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACC,IAAI,KAAK,QAAQ,GAAGC,UAAU,CAACJ,KAAK,CAACE,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IACvGrD,WAAW,CAACyD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACN,KAAK,GAAGE;IACX,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIF,KAAK,IAAIxC,MAAM,EAAE;MACnBC,SAAS,CAAC6C,IAAI,IAAI;QAChB,MAAMC,SAAS,GAAG;UAAE,GAAGD;QAAK,CAAC;QAC7B,OAAOC,SAAS,CAACP,KAAK,CAAC;QACvB,OAAOO,SAAS;MAClB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAACP,KAAU,EAAEQ,QAAa,KAAK;IACvDlD,gBAAgB,CAACkD,QAAQ,CAAC;IAC1B5D,WAAW,CAACyD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPxD,QAAQ,EAAE,CAAA2D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEhE,EAAE,KAAI;IAC5B,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIe,MAAM,CAACV,QAAQ,EAAE;MACnBW,SAAS,CAAC6C,IAAI,IAAI;QAChB,MAAMC,SAAS,GAAG;UAAE,GAAGD;QAAK,CAAC;QAC7B,OAAOC,SAAS,CAACzD,QAAQ;QACzB,OAAOyD,SAAS;MAClB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMG,YAAY,GAAIT,KAAsB,IAAK;IAC/CA,KAAK,CAACU,cAAc,CAAC,CAAC;;IAEtB;IACA,MAAMJ,SAAiC,GAAG,CAAC,CAAC;IAE5C,IAAI,CAAC3D,QAAQ,CAACE,QAAQ,EAAE;MACtByD,SAAS,CAACzD,QAAQ,GAAG,wBAAwB;IAC/C;IAEA,IAAIF,QAAQ,CAACG,QAAQ,IAAI,CAAC,EAAE;MAC1BwD,SAAS,CAACxD,QAAQ,GAAG,iCAAiC;IACxD;IAEA,IAAIH,QAAQ,CAACI,cAAc,IAAI,CAAC,EAAE;MAChCuD,SAAS,CAACvD,cAAc,GAAG,uCAAuC;IACpE;IAEA,IAAI,CAACJ,QAAQ,CAACK,aAAa,EAAE;MAC3BsD,SAAS,CAACtD,aAAa,GAAG,2BAA2B;IACvD;IAEA,IAAI2D,MAAM,CAACC,IAAI,CAACN,SAAS,CAAC,CAAC7B,MAAM,GAAG,CAAC,EAAE;MACrCjB,SAAS,CAAC8C,SAAS,CAAC;MACpB;IACF;IAEAhB,kBAAkB,CAACuB,MAAM,CAAClE,QAAQ,CAAC;EACrC,CAAC;EAED,MAAMmE,mBAAmB,GAAGA,CAAA,KAAM;IAChC,OAAOnE,QAAQ,CAACG,QAAQ,GAAGH,QAAQ,CAACI,cAAc;EACpD,CAAC;EAED,IAAI,CAACN,WAAW,EAAE;IAChB,oBACEP,OAAA,CAACrB,GAAG;MAACkG,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,EAAE,EAAE,MAAM;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,eAC3CjF,OAAA,CAACpB,UAAU;QAACsG,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,OAAO;QAAAF,QAAA,EAAC;MAEvC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACEvF,OAAA,CAAAE,SAAA;IAAA+E,QAAA,gBACEjF,OAAA,CAACT,MAAM;MAAA0F,QAAA,gBACLjF,OAAA;QAAAiF,QAAA,EAAO;MAAwB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACvCvF,OAAA;QAAMyC,IAAI,EAAC,aAAa;QAAC+C,OAAO,EAAC;MAAqC;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,eAETvF,OAAA,CAACrB,GAAG;MAACkG,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,EAAE,EAAE,MAAM;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAE3CjF,OAAA,CAACrB,GAAG;QAAC8G,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,gBAC5CjF,OAAA,CAACjB,MAAM;UACL6G,SAAS,eAAE5F,OAAA,CAACX,SAAS;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBM,OAAO,EAAEA,CAAA,KAAMxF,QAAQ,CAAC,eAAeE,WAAW,EAAE,CAAE;UACtDsE,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,EACf;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvF,OAAA,CAACrB,GAAG;UAAAsG,QAAA,gBACFjF,OAAA,CAACpB,UAAU;YAACsG,OAAO,EAAC,IAAI;YAACa,SAAS,EAAC,IAAI;YAAAd,QAAA,EAAC;UAExC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZtC,SAAS,iBACRjD,OAAA,CAACpB,UAAU;YAACsG,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,GAAC,KAC9C,EAAChC,SAAS,CAACR,IAAI;UAAA;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvF,OAAA,CAACnB,KAAK;QAACgG,EAAE,EAAE;UAAEG,CAAC,EAAE;QAAE,CAAE;QAAAC,QAAA,eAClBjF,OAAA;UAAMgG,QAAQ,EAAEzB,YAAa;UAAAU,QAAA,eAC3BjF,OAAA,CAACb,IAAI;YAAC8G,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAjB,QAAA,gBAEzBjF,OAAA,CAACb,IAAI;cAACgH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAnB,QAAA,EACfrD,WAAW,IAAI,CAACO,YAAY,CAACI,MAAM,gBAClCvC,OAAA,CAACf,KAAK;gBAACoH,QAAQ,EAAC,SAAS;gBAACxB,EAAE,EAAE;kBAAEc,EAAE,EAAE;gBAAE,CAAE;gBAAAV,QAAA,GAAC,mEAEvC,eAAAjF,OAAA;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNvF,OAAA;kBAAAiF,QAAA,EAAO;gBAAyE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC,GACN,CAAC7D,aAAa,IAAIS,YAAY,CAACI,MAAM,KAAK,CAAC,gBAC7CvC,OAAA,CAACf,KAAK;gBAACoH,QAAQ,EAAC,SAAS;gBAACxB,EAAE,EAAE;kBAAEc,EAAE,EAAE;gBAAE,CAAE;gBAAAV,QAAA,EAAC;cAEzC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,gBAERvF,OAAA,CAAChB,YAAY;gBACXsH,OAAO,EAAEnE,YAAa;gBACtBoE,cAAc,EAAGC,MAAM,IAAK;kBAC1B,IAAI,CAACA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE,OAAO,EAAE;kBACpD,OAAO,GAAGA,MAAM,CAAChE,MAAM,IAAI,KAAK,MAAMgE,MAAM,CAAC/D,IAAI,IAAI,eAAe,EAAE;gBACxE,CAAE;gBACFsB,KAAK,EAAE5C,aAAc;gBACrBsF,QAAQ,EAAEpC,iBAAkB;gBAC5BqC,OAAO,EAAEhF,aAAc;gBACvBiF,QAAQ,EAAEjF,aAAa,IAAI,CAAC,CAACE,WAAW,IAAIO,YAAY,CAACI,MAAM,KAAK,CAAE;gBACtEqE,oBAAoB,EAAEA,CAACJ,MAAM,EAAEzC,KAAK,KAAK;kBACvC,IAAI,CAACyC,MAAM,IAAI,CAACzC,KAAK,EAAE,OAAO,KAAK;kBACnC,OAAOyC,MAAM,CAAClG,EAAE,KAAKyD,KAAK,CAACzD,EAAE;gBAC/B,CAAE;gBACFuG,aAAa,EAAEnF,aAAa,GAAG,YAAY,GAAG,iBAAkB;gBAChEoF,WAAW,EAAGC,MAAM,iBAClB/G,OAAA,CAAClB,SAAS;kBAAA,GACJiI,MAAM;kBACVC,KAAK,EAAC,cAAc;kBACpBrF,KAAK,EAAE,CAAC,CAACN,MAAM,CAACV,QAAS;kBACzBsG,UAAU,EACR5F,MAAM,CAACV,QAAQ,KACde,aAAa,GAAG,mBAAmB,GACnCS,YAAY,CAACI,MAAM,KAAK,CAAC,GAAG,qBAAqB,GACjD,GAAGJ,YAAY,CAACI,MAAM,mBAAmB,CAC3C;kBACD2E,QAAQ;kBACRC,UAAU,EAAE;oBACV,GAAGJ,MAAM,CAACI,UAAU;oBACpBC,YAAY,eACVpH,OAAA,CAAAE,SAAA;sBAAA+E,QAAA,GACGvD,aAAa,gBAAG1B,OAAA,CAACd,gBAAgB;wBAACiG,KAAK,EAAC,SAAS;wBAACkC,IAAI,EAAE;sBAAG;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,GAAG,IAAI,EACrEwB,MAAM,CAACI,UAAU,CAACC,YAAY;oBAAA,eAC/B;kBAEN;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACD;gBACF+B,YAAY,EAAEA,CAACC,KAAK,EAAEf,MAAM,kBAC1B9H,cAAA,CAACC,GAAG;kBAACoH,SAAS,EAAC,IAAI;kBAAA,GAAKwB,KAAK;kBAAEC,GAAG,EAAEhB,MAAM,CAAClG,EAAE,IAAImH,IAAI,CAACC,MAAM,CAAC,CAAE;kBAAAC,MAAA;kBAAAC,QAAA;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBAAA,gBAC7DvF,OAAA,CAACrB,GAAG;kBAAAsG,QAAA,gBACFjF,OAAA,CAACpB,UAAU;oBAACsG,OAAO,EAAC,OAAO;oBAAAD,QAAA,GACxBuB,MAAM,CAAChE,MAAM,IAAI,KAAK,EAAC,KAAG,EAACgE,MAAM,CAAC/D,IAAI,IAAI,eAAe;kBAAA;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACbvF,OAAA,CAACpB,UAAU;oBAACsG,OAAO,EAAC,OAAO;oBAACC,KAAK,EAAC,gBAAgB;oBAAAF,QAAA,GAC/CuB,MAAM,CAAC9D,UAAU,IAAI,SAAS,EAAC,UAAG,EAAC8D,MAAM,CAAC7D,MAAM,IAAI,KAAK;kBAAA;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CACF;cACL;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAGPvF,OAAA,CAACb,IAAI;cAACgH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACyB,EAAE,EAAE,CAAE;cAAA5C,QAAA,eACvBjF,OAAA,CAAClB,SAAS;gBACRkI,KAAK,EAAC,UAAU;gBAChB/C,IAAI,EAAC,QAAQ;gBACbF,KAAK,EAAEtD,QAAQ,CAACG,QAAQ,IAAI,EAAG;gBAC/B6F,QAAQ,EAAE7C,iBAAiB,CAAC,UAAU,CAAE;gBACxCjC,KAAK,EAAE,CAAC,CAACN,MAAM,CAACT,QAAS;gBACzBqG,UAAU,EAAE5F,MAAM,CAACT,QAAS;gBAC5BsG,QAAQ;gBACRY,SAAS;gBACTC,UAAU,EAAE;kBAAEC,GAAG,EAAE,CAAC;kBAAEC,IAAI,EAAE;gBAAK;cAAE;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPvF,OAAA,CAACb,IAAI;cAACgH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACyB,EAAE,EAAE,CAAE;cAAA5C,QAAA,eACvBjF,OAAA,CAAClB,SAAS;gBACRkI,KAAK,EAAC,gBAAgB;gBACtB/C,IAAI,EAAC,QAAQ;gBACbF,KAAK,EAAEtD,QAAQ,CAACI,cAAc,IAAI,EAAG;gBACrC4F,QAAQ,EAAE7C,iBAAiB,CAAC,gBAAgB,CAAE;gBAC9CjC,KAAK,EAAE,CAAC,CAACN,MAAM,CAACR,cAAe;gBAC/BoG,UAAU,EAAE5F,MAAM,CAACR,cAAe;gBAClCqG,QAAQ;gBACRY,SAAS;gBACTX,UAAU,EAAE;kBACVe,cAAc,eAAElI,OAAA,CAACZ,cAAc;oBAAC+I,QAAQ,EAAC,OAAO;oBAAAlD,QAAA,EAAC;kBAAC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAgB;gBACpE,CAAE;gBACFwC,UAAU,EAAE;kBAAEC,GAAG,EAAE,CAAC;kBAAEC,IAAI,EAAE;gBAAK;cAAE;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPvF,OAAA,CAACb,IAAI;cAACgH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACyB,EAAE,EAAE,CAAE;cAAA5C,QAAA,eACvBjF,OAAA,CAAClB,SAAS;gBACRkI,KAAK,EAAC,eAAe;gBACrB/C,IAAI,EAAC,MAAM;gBACXF,KAAK,EAAEtD,QAAQ,CAACK,aAAc;gBAC9B2F,QAAQ,EAAE7C,iBAAiB,CAAC,eAAe,CAAE;gBAC7CjC,KAAK,EAAE,CAAC,CAACN,MAAM,CAACP,aAAc;gBAC9BmG,UAAU,EAAE5F,MAAM,CAACP,aAAc;gBACjCoG,QAAQ;gBACRY,SAAS;gBACTM,eAAe,EAAE;kBACfC,MAAM,EAAE;gBACV;cAAE;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPvF,OAAA,CAACb,IAAI;cAACgH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACyB,EAAE,EAAE,CAAE;cAAA5C,QAAA,eACvBjF,OAAA,CAAClB,SAAS;gBACRkI,KAAK,EAAC,aAAa;gBACnBjD,KAAK,EAAE,IAAIa,mBAAmB,CAAC,CAAC,CAAC0D,OAAO,CAAC,CAAC,CAAC,EAAG;gBAC9CR,SAAS;gBACTnB,QAAQ;gBACRQ,UAAU,EAAE;kBACVoB,QAAQ,EAAE;gBACZ;cAAE;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPvF,OAAA,CAACb,IAAI;cAACgH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAnB,QAAA,eAChBjF,OAAA,CAAClB,SAAS;gBACRkI,KAAK,EAAC,kBAAkB;gBACxBjD,KAAK,EAAEtD,QAAQ,CAACS,KAAM;gBACtBuF,QAAQ,EAAE7C,iBAAiB,CAAC,OAAO,CAAE;gBACrC4E,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRX,SAAS;gBACTY,WAAW,EAAC;cAAqC;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGNd,MAAM,CAACC,IAAI,CAACrD,MAAM,CAAC,CAACkB,MAAM,GAAG,CAAC,iBAC7BvC,OAAA,CAACb,IAAI;cAACgH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAnB,QAAA,eAChBjF,OAAA,CAACf,KAAK;gBAACoH,QAAQ,EAAC,OAAO;gBAAApB,QAAA,EAAC;cAExB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACP,eAGDvF,OAAA,CAACb,IAAI;cAACgH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAnB,QAAA,eAChBjF,OAAA,CAACrB,GAAG;gBAAC8G,OAAO,EAAC,MAAM;gBAACkD,cAAc,EAAC,UAAU;gBAACC,GAAG,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA5D,QAAA,gBAC1DjF,OAAA,CAACjB,MAAM;kBACLmG,OAAO,EAAC,UAAU;kBAClBW,OAAO,EAAEA,CAAA,KAAMxF,QAAQ,CAAC,eAAeE,WAAW,EAAE,CAAE;kBACtDoG,QAAQ,EAAEvD,kBAAkB,CAAC3B,SAAU;kBAAAwD,QAAA,EACxC;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTvF,OAAA,CAACjB,MAAM;kBACLkF,IAAI,EAAC,QAAQ;kBACbiB,OAAO,EAAC,WAAW;kBACnBU,SAAS,EAAExC,kBAAkB,CAAC3B,SAAS,gBAAGzB,OAAA,CAACd,gBAAgB;oBAACmI,IAAI,EAAE;kBAAG;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGvF,OAAA,CAACV,GAAG;oBAAA8F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnFoB,QAAQ,EAAEvD,kBAAkB,CAAC3B,SAAU;kBAAAwD,QAAA,EAEtC7B,kBAAkB,CAAC3B,SAAS,GAAG,WAAW,GAAG;gBAAa;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGRvF,OAAA,CAACnB,KAAK;QAACgG,EAAE,EAAE;UAAEG,CAAC,EAAE,CAAC;UAAE6D,EAAE,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAqB,CAAE;QAAA7D,QAAA,gBACxDjF,OAAA,CAACpB,UAAU;UAACsG,OAAO,EAAC,IAAI;UAAC6D,YAAY;UAAA9D,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvF,OAAA,CAACpB,UAAU;UAACsG,OAAO,EAAC,OAAO;UAACC,KAAK,EAAC,gBAAgB;UAAAF,QAAA,EAAC;QAEnD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvF,OAAA,CAACrB,GAAG;UAACoH,SAAS,EAAC,IAAI;UAAClB,EAAE,EAAE;YAAEgE,EAAE,EAAE,CAAC;YAAEG,EAAE,EAAE;UAAE,CAAE;UAAA/D,QAAA,gBACvCjF,OAAA,CAACpB,UAAU;YAACmH,SAAS,EAAC,IAAI;YAACb,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,EAAC;UAElE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvF,OAAA,CAACpB,UAAU;YAACmH,SAAS,EAAC,IAAI;YAACb,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,EAAC;UAElE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvF,OAAA,CAACpB,UAAU;YAACmH,SAAS,EAAC,IAAI;YAACb,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,EAAC;UAElE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvF,OAAA,CAACpB,UAAU;YAACmH,SAAS,EAAC,IAAI;YAACb,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,EAAC;UAElE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACnF,EAAA,CAjcID,cAAwB;EAAA,QACXX,WAAW,EACAC,SAAS,EACjBG,cAAc,EAcqCD,QAAQ,EAsFnDA,QAAQ,EAQTD,WAAW;AAAA;AAAAuJ,EAAA,GA/GlC9I,cAAwB;AAmc9B,eAAeA,cAAc;AAAC,IAAA8I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}