{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\pages\\\\Portfolio\\\\AnalyticsPage.tsx\",\n  _s = $RefreshSig$();\n// TrustVault - Portfolio Analytics Page\n\nimport React, { useState } from 'react';\nimport { Box, Typography, Button, Grid, Card, CardContent, Paper, Chip, FormControl, InputLabel, Select, MenuItem, LinearProgress } from '@mui/material';\nimport { ArrowBack, TrendingUp, TrendingDown, PieChart, ShowChart, Assessment } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useQuery } from 'react-query';\n\n// Services\nimport apiService from '../../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AnalyticsPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    id: portfolioId\n  } = useParams();\n  const [timeRange, setTimeRange] = useState('1M');\n\n  // Fetch portfolio details\n  const {\n    data: portfolio\n  } = useQuery(['portfolio', portfolioId], () => apiService.getPortfolio(portfolioId), {\n    enabled: !!portfolioId\n  });\n\n  // Fetch analytics data\n  const {\n    data: analytics,\n    isLoading: analyticsLoading\n  } = useQuery(['analytics', portfolioId], () => apiService.getPortfolioAnalytics(portfolioId), {\n    enabled: !!portfolioId\n  });\n\n  // Fetch holdings for allocation analysis\n  const {\n    data: holdings\n  } = useQuery(['holdings', portfolioId], () => apiService.getHoldings(portfolioId), {\n    enabled: !!portfolioId\n  });\n  const formatCurrency = value => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(typeof value === 'string' ? parseFloat(value) : value);\n  };\n  const formatPercentage = value => {\n    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;\n  };\n\n  // Calculate portfolio metrics\n  const totalValue = (analytics === null || analytics === void 0 ? void 0 : analytics.total_value) || (portfolio ? parseFloat(portfolio.total_value) : 0);\n\n  // Performance metrics from analytics or mock data\n  const performanceMetrics = {\n    totalReturn: (analytics === null || analytics === void 0 ? void 0 : analytics.total_profit_loss) || 1250.75,\n    totalReturnPercent: (analytics === null || analytics === void 0 ? void 0 : analytics.total_profit_loss_percentage) || 8.45,\n    totalValue: (analytics === null || analytics === void 0 ? void 0 : analytics.total_value) || totalValue,\n    totalCost: (analytics === null || analytics === void 0 ? void 0 : analytics.total_cost) || 0,\n    dayChange: -45.32,\n    // This would come from real-time data\n    dayChangePercent: -0.32,\n    weekChange: 125.50,\n    weekChangePercent: 0.89,\n    monthChange: 450.25,\n    monthChangePercent: 3.21\n  };\n\n  // Use analytics data if available, otherwise use holdings data\n  const allocationData = (analytics === null || analytics === void 0 ? void 0 : analytics.asset_allocation) || (holdings === null || holdings === void 0 ? void 0 : holdings.map(holding => ({\n    symbol: holding.asset.symbol,\n    name: holding.asset.name,\n    value: parseFloat(holding.current_value),\n    percentage: parseFloat(holding.current_value) / totalValue * 100,\n    sector: holding.asset.sector\n  }))) || [];\n\n  // Use analytics sector data if available, otherwise calculate from holdings\n  const sectorAllocation = (analytics === null || analytics === void 0 ? void 0 : analytics.sector_allocation) || allocationData.reduce((acc, holding) => {\n    const sector = holding.sector || 'Other';\n    if (!(sector in acc)) {\n      acc[sector] = {\n        value: 0,\n        percentage: 0\n      };\n    }\n    acc[sector].value += holding.value;\n    acc[sector].percentage += holding.percentage;\n    return acc;\n  }, {});\n  if (!portfolioId) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: 1200,\n        mx: 'auto',\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"error\",\n        children: \"Portfolio ID is required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this);\n  }\n  if (analyticsLoading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: 1200,\n        mx: 'auto',\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Loading analytics...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Analytics - TrustVault\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Portfolio performance analytics and insights\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: 1200,\n        mx: 'auto',\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 26\n            }, this),\n            onClick: () => navigate(`/portfolios/${portfolioId}`),\n            sx: {\n              mr: 2\n            },\n            children: \"Back to Portfolio\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              component: \"h1\",\n              children: \"Portfolio Analytics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), portfolio && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: portfolio.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          size: \"small\",\n          sx: {\n            minWidth: 120\n          },\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"Time Range\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: timeRange,\n            onChange: e => setTimeRange(e.target.value),\n            label: \"Time Range\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"1D\",\n              children: \"1 Day\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"1W\",\n              children: \"1 Week\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"1M\",\n              children: \"1 Month\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"3M\",\n              children: \"3 Months\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"6M\",\n              children: \"6 Months\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"1Y\",\n              children: \"1 Year\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"ALL\",\n              children: \"All Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Performance Overview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 3,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 3,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  variant: \"outlined\",\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      mb: 1,\n                      children: [/*#__PURE__*/_jsxDEV(Assessment, {\n                        color: \"primary\",\n                        sx: {\n                          mr: 1\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 195,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Total Value\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 196,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      fontWeight: \"bold\",\n                      children: formatCurrency(totalValue)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 200,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 3,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  variant: \"outlined\",\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      mb: 1,\n                      children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                        color: \"success\",\n                        sx: {\n                          mr: 1\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 211,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Total Return\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 212,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 210,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      fontWeight: \"bold\",\n                      color: \"success.main\",\n                      children: formatCurrency(performanceMetrics.totalReturn)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"success.main\",\n                      children: formatPercentage(performanceMetrics.totalReturnPercent)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 3,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  variant: \"outlined\",\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      mb: 1,\n                      children: [/*#__PURE__*/_jsxDEV(TrendingDown, {\n                        color: \"error\",\n                        sx: {\n                          mr: 1\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 230,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Day Change\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 231,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      fontWeight: \"bold\",\n                      color: \"error.main\",\n                      children: formatCurrency(performanceMetrics.dayChange)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 235,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"error.main\",\n                      children: formatPercentage(performanceMetrics.dayChangePercent)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 3,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  variant: \"outlined\",\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      mb: 1,\n                      children: [/*#__PURE__*/_jsxDEV(ShowChart, {\n                        color: \"info\",\n                        sx: {\n                          mr: 1\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 249,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Month Change\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 250,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      fontWeight: \"bold\",\n                      color: \"success.main\",\n                      children: formatCurrency(performanceMetrics.monthChange)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 254,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"success.main\",\n                      children: formatPercentage(performanceMetrics.monthChangePercent)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(PieChart, {\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                children: \"Asset Allocation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), allocationData.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              py: 4,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"No holdings to display\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              children: allocationData.slice(0, 5).map((holding, index) => /*#__PURE__*/_jsxDEV(Box, {\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  mb: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: holding.symbol\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 289,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: holding.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 292,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    textAlign: \"right\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: [holding.percentage.toFixed(1), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 297,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: formatCurrency(holding.value)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 300,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: holding.percentage,\n                  sx: {\n                    height: 6,\n                    borderRadius: 3\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 23\n                }, this)]\n              }, holding.symbol, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Sector Allocation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this), Object.keys(sectorAllocation).length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              py: 4,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"No sector data available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              children: Object.entries(sectorAllocation).map(([sector, data]) => /*#__PURE__*/_jsxDEV(Box, {\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  mb: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    children: sector\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    textAlign: \"right\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: [data.percentage.toFixed(1), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 339,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: formatCurrency(data.value)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: data.percentage,\n                  sx: {\n                    height: 6,\n                    borderRadius: 3\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 23\n                }, this)]\n              }, sector, true, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Performance Chart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                height: 300,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                bgcolor: 'background.default',\n                borderRadius: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                textAlign: \"center\",\n                children: [/*#__PURE__*/_jsxDEV(ShowChart, {\n                  sx: {\n                    fontSize: 48,\n                    color: 'text.secondary',\n                    mb: 2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: \"Performance Chart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Interactive chart showing portfolio performance over time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Coming Soon\",\n                  color: \"primary\",\n                  size: \"small\",\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AnalyticsPage, \"R0DbjeKYg/zjyFDFmN8wiw0ZK40=\", false, function () {\n  return [useNavigate, useParams, useQuery, useQuery, useQuery];\n});\n_c = AnalyticsPage;\nexport default AnalyticsPage;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsPage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Paper", "Chip", "FormControl", "InputLabel", "Select", "MenuItem", "LinearProgress", "ArrowBack", "TrendingUp", "TrendingDown", "<PERSON><PERSON><PERSON>", "ShowChart", "Assessment", "<PERSON><PERSON><PERSON>", "useNavigate", "useParams", "useQuery", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AnalyticsPage", "_s", "navigate", "id", "portfolioId", "timeRange", "setTimeRange", "data", "portfolio", "getPortfolio", "enabled", "analytics", "isLoading", "analyticsLoading", "getPortfolioAnalytics", "holdings", "getHoldings", "formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "format", "parseFloat", "formatPercentage", "toFixed", "totalValue", "total_value", "performanceMetrics", "totalReturn", "total_profit_loss", "totalReturnPercent", "total_profit_loss_percentage", "totalCost", "total_cost", "day<PERSON><PERSON>e", "dayChangePercent", "weekChange", "weekChangePercent", "monthChange", "monthChangePercent", "allocationData", "asset_allocation", "map", "holding", "symbol", "asset", "name", "current_value", "percentage", "sector", "sectorAllocation", "sector_allocation", "reduce", "acc", "sx", "max<PERSON><PERSON><PERSON>", "mx", "p", "children", "variant", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "display", "alignItems", "justifyContent", "mb", "startIcon", "onClick", "mr", "component", "size", "min<PERSON><PERSON><PERSON>", "onChange", "e", "target", "label", "container", "spacing", "item", "xs", "gutterBottom", "sm", "md", "fontWeight", "length", "textAlign", "py", "slice", "index", "height", "borderRadius", "Object", "keys", "entries", "bgcolor", "fontSize", "mt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/pages/Portfolio/AnalyticsPage.tsx"], "sourcesContent": ["// TrustVault - Portfolio Analytics Page\n\nimport React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Paper,\n  Chip,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  LinearProgress,\n} from '@mui/material';\nimport {\n  ArrowBack,\n  TrendingUp,\n  TrendingDown,\n  PieChart,\n  ShowChart,\n  Assessment,\n} from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useQuery } from 'react-query';\n\n// Services\nimport apiService from '../../services/api';\n\nconst AnalyticsPage: React.FC = () => {\n  const navigate = useNavigate();\n  const { id: portfolioId } = useParams<{ id: string }>();\n  \n  const [timeRange, setTimeRange] = useState<string>('1M');\n\n  // Fetch portfolio details\n  const { data: portfolio } = useQuery(\n    ['portfolio', portfolioId],\n    () => apiService.getPortfolio(portfolioId!),\n    {\n      enabled: !!portfolioId,\n    }\n  );\n\n  // Fetch analytics data\n  const { data: analytics, isLoading: analyticsLoading } = useQuery(\n    ['analytics', portfolioId],\n    () => apiService.getPortfolioAnalytics(portfolioId!),\n    {\n      enabled: !!portfolioId,\n    }\n  );\n\n  // Fetch holdings for allocation analysis\n  const { data: holdings } = useQuery(\n    ['holdings', portfolioId],\n    () => apiService.getHoldings(portfolioId!),\n    {\n      enabled: !!portfolioId,\n    }\n  );\n\n  const formatCurrency = (value: string | number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(typeof value === 'string' ? parseFloat(value) : value);\n  };\n\n  const formatPercentage = (value: number) => {\n    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;\n  };\n\n  // Calculate portfolio metrics\n  const totalValue = analytics?.total_value || (portfolio ? parseFloat(portfolio.total_value) : 0);\n\n  // Performance metrics from analytics or mock data\n  const performanceMetrics = {\n    totalReturn: analytics?.total_profit_loss || 1250.75,\n    totalReturnPercent: analytics?.total_profit_loss_percentage || 8.45,\n    totalValue: analytics?.total_value || totalValue,\n    totalCost: analytics?.total_cost || 0,\n    dayChange: -45.32, // This would come from real-time data\n    dayChangePercent: -0.32,\n    weekChange: 125.50,\n    weekChangePercent: 0.89,\n    monthChange: 450.25,\n    monthChangePercent: 3.21,\n  };\n\n  // Use analytics data if available, otherwise use holdings data\n  const allocationData = analytics?.asset_allocation || holdings?.map((holding: any) => ({\n    symbol: holding.asset.symbol,\n    name: holding.asset.name,\n    value: parseFloat(holding.current_value),\n    percentage: (parseFloat(holding.current_value) / totalValue) * 100,\n    sector: holding.asset.sector,\n  })) || [];\n\n  // Use analytics sector data if available, otherwise calculate from holdings\n  const sectorAllocation = analytics?.sector_allocation || allocationData.reduce((acc: Record<string, any>, holding: any) => {\n    const sector = holding.sector || 'Other';\n    if (!(sector in acc)) {\n      acc[sector] = { value: 0, percentage: 0 };\n    }\n    acc[sector].value += holding.value;\n    acc[sector].percentage += holding.percentage;\n    return acc;\n  }, {} as Record<string, any>);\n\n  if (!portfolioId) {\n    return (\n      <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>\n        <Typography variant=\"h6\" color=\"error\">\n          Portfolio ID is required\n        </Typography>\n      </Box>\n    );\n  }\n\n  if (analyticsLoading) {\n    return (\n      <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>\n        <Typography variant=\"h6\">\n          Loading analytics...\n        </Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <>\n      <Helmet>\n        <title>Analytics - TrustVault</title>\n        <meta name=\"description\" content=\"Portfolio performance analytics and insights\" />\n      </Helmet>\n\n      <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>\n        {/* Header */}\n        <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\" mb={3}>\n          <Box display=\"flex\" alignItems=\"center\">\n            <Button\n              startIcon={<ArrowBack />}\n              onClick={() => navigate(`/portfolios/${portfolioId}`)}\n              sx={{ mr: 2 }}\n            >\n              Back to Portfolio\n            </Button>\n            <Box>\n              <Typography variant=\"h4\" component=\"h1\">\n                Portfolio Analytics\n              </Typography>\n              {portfolio && (\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  {portfolio.name}\n                </Typography>\n              )}\n            </Box>\n          </Box>\n          \n          <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n            <InputLabel>Time Range</InputLabel>\n            <Select\n              value={timeRange}\n              onChange={(e) => setTimeRange(e.target.value)}\n              label=\"Time Range\"\n            >\n              <MenuItem value=\"1D\">1 Day</MenuItem>\n              <MenuItem value=\"1W\">1 Week</MenuItem>\n              <MenuItem value=\"1M\">1 Month</MenuItem>\n              <MenuItem value=\"3M\">3 Months</MenuItem>\n              <MenuItem value=\"6M\">6 Months</MenuItem>\n              <MenuItem value=\"1Y\">1 Year</MenuItem>\n              <MenuItem value=\"ALL\">All Time</MenuItem>\n            </Select>\n          </FormControl>\n        </Box>\n\n        <Grid container spacing={3}>\n          {/* Performance Overview */}\n          <Grid item xs={12}>\n            <Paper sx={{ p: 3 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                Performance Overview\n              </Typography>\n              <Grid container spacing={3}>\n                <Grid item xs={12} sm={6} md={3}>\n                  <Card variant=\"outlined\">\n                    <CardContent>\n                      <Box display=\"flex\" alignItems=\"center\" mb={1}>\n                        <Assessment color=\"primary\" sx={{ mr: 1 }} />\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Total Value\n                        </Typography>\n                      </Box>\n                      <Typography variant=\"h5\" fontWeight=\"bold\">\n                        {formatCurrency(totalValue)}\n                      </Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n                \n                <Grid item xs={12} sm={6} md={3}>\n                  <Card variant=\"outlined\">\n                    <CardContent>\n                      <Box display=\"flex\" alignItems=\"center\" mb={1}>\n                        <TrendingUp color=\"success\" sx={{ mr: 1 }} />\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Total Return\n                        </Typography>\n                      </Box>\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"success.main\">\n                        {formatCurrency(performanceMetrics.totalReturn)}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"success.main\">\n                        {formatPercentage(performanceMetrics.totalReturnPercent)}\n                      </Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n                \n                <Grid item xs={12} sm={6} md={3}>\n                  <Card variant=\"outlined\">\n                    <CardContent>\n                      <Box display=\"flex\" alignItems=\"center\" mb={1}>\n                        <TrendingDown color=\"error\" sx={{ mr: 1 }} />\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Day Change\n                        </Typography>\n                      </Box>\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"error.main\">\n                        {formatCurrency(performanceMetrics.dayChange)}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"error.main\">\n                        {formatPercentage(performanceMetrics.dayChangePercent)}\n                      </Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n                \n                <Grid item xs={12} sm={6} md={3}>\n                  <Card variant=\"outlined\">\n                    <CardContent>\n                      <Box display=\"flex\" alignItems=\"center\" mb={1}>\n                        <ShowChart color=\"info\" sx={{ mr: 1 }} />\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Month Change\n                        </Typography>\n                      </Box>\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"success.main\">\n                        {formatCurrency(performanceMetrics.monthChange)}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"success.main\">\n                        {formatPercentage(performanceMetrics.monthChangePercent)}\n                      </Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              </Grid>\n            </Paper>\n          </Grid>\n\n          {/* Asset Allocation */}\n          <Grid item xs={12} md={6}>\n            <Paper sx={{ p: 3 }}>\n              <Box display=\"flex\" alignItems=\"center\" mb={3}>\n                <PieChart color=\"primary\" sx={{ mr: 1 }} />\n                <Typography variant=\"h6\">\n                  Asset Allocation\n                </Typography>\n              </Box>\n              \n              {allocationData.length === 0 ? (\n                <Box textAlign=\"center\" py={4}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    No holdings to display\n                  </Typography>\n                </Box>\n              ) : (\n                <Box>\n                  {allocationData.slice(0, 5).map((holding: any, index: number) => (\n                    <Box key={holding.symbol} mb={2}>\n                      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={1}>\n                        <Box>\n                          <Typography variant=\"body2\" fontWeight=\"medium\">\n                            {holding.symbol}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            {holding.name}\n                          </Typography>\n                        </Box>\n                        <Box textAlign=\"right\">\n                          <Typography variant=\"body2\" fontWeight=\"medium\">\n                            {holding.percentage.toFixed(1)}%\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            {formatCurrency(holding.value)}\n                          </Typography>\n                        </Box>\n                      </Box>\n                      <LinearProgress\n                        variant=\"determinate\"\n                        value={holding.percentage}\n                        sx={{ height: 6, borderRadius: 3 }}\n                      />\n                    </Box>\n                  ))}\n                </Box>\n              )}\n            </Paper>\n          </Grid>\n\n          {/* Sector Allocation */}\n          <Grid item xs={12} md={6}>\n            <Paper sx={{ p: 3 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                Sector Allocation\n              </Typography>\n              \n              {Object.keys(sectorAllocation).length === 0 ? (\n                <Box textAlign=\"center\" py={4}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    No sector data available\n                  </Typography>\n                </Box>\n              ) : (\n                <Box>\n                  {Object.entries(sectorAllocation).map(([sector, data]: [string, any]) => (\n                    <Box key={sector} mb={2}>\n                      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={1}>\n                        <Typography variant=\"body2\" fontWeight=\"medium\">\n                          {sector}\n                        </Typography>\n                        <Box textAlign=\"right\">\n                          <Typography variant=\"body2\" fontWeight=\"medium\">\n                            {data.percentage.toFixed(1)}%\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            {formatCurrency(data.value)}\n                          </Typography>\n                        </Box>\n                      </Box>\n                      <LinearProgress\n                        variant=\"determinate\"\n                        value={data.percentage}\n                        sx={{ height: 6, borderRadius: 3 }}\n                      />\n                    </Box>\n                  ))}\n                </Box>\n              )}\n            </Paper>\n          </Grid>\n\n          {/* Performance Chart Placeholder */}\n          <Grid item xs={12}>\n            <Paper sx={{ p: 3 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                Performance Chart\n              </Typography>\n              <Box\n                sx={{\n                  height: 300,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  bgcolor: 'background.default',\n                  borderRadius: 1,\n                }}\n              >\n                <Box textAlign=\"center\">\n                  <ShowChart sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />\n                  <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                    Performance Chart\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Interactive chart showing portfolio performance over time\n                  </Typography>\n                  <Chip label=\"Coming Soon\" color=\"primary\" size=\"small\" sx={{ mt: 1 }} />\n                </Box>\n              </Box>\n            </Paper>\n          </Grid>\n        </Grid>\n      </Box>\n    </>\n  );\n};\n\nexport default AnalyticsPage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,QACT,eAAe;AACtB,SACEC,SAAS,EACTC,UAAU,EACVC,YAAY,EACZC,QAAQ,EACRC,SAAS,EACTC,UAAU,QACL,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,QAAQ,QAAQ,aAAa;;AAEtC;AACA,OAAOC,UAAU,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW,EAAE,EAAEC;EAAY,CAAC,GAAGX,SAAS,CAAiB,CAAC;EAEvD,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAS,IAAI,CAAC;;EAExD;EACA,MAAM;IAAEoC,IAAI,EAAEC;EAAU,CAAC,GAAGd,QAAQ,CAClC,CAAC,WAAW,EAAEU,WAAW,CAAC,EAC1B,MAAMT,UAAU,CAACc,YAAY,CAACL,WAAY,CAAC,EAC3C;IACEM,OAAO,EAAE,CAAC,CAACN;EACb,CACF,CAAC;;EAED;EACA,MAAM;IAAEG,IAAI,EAAEI,SAAS;IAAEC,SAAS,EAAEC;EAAiB,CAAC,GAAGnB,QAAQ,CAC/D,CAAC,WAAW,EAAEU,WAAW,CAAC,EAC1B,MAAMT,UAAU,CAACmB,qBAAqB,CAACV,WAAY,CAAC,EACpD;IACEM,OAAO,EAAE,CAAC,CAACN;EACb,CACF,CAAC;;EAED;EACA,MAAM;IAAEG,IAAI,EAAEQ;EAAS,CAAC,GAAGrB,QAAQ,CACjC,CAAC,UAAU,EAAEU,WAAW,CAAC,EACzB,MAAMT,UAAU,CAACqB,WAAW,CAACZ,WAAY,CAAC,EAC1C;IACEM,OAAO,EAAE,CAAC,CAACN;EACb,CACF,CAAC;EAED,MAAMa,cAAc,GAAIC,KAAsB,IAAK;IACjD,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAAC,OAAOL,KAAK,KAAK,QAAQ,GAAGM,UAAU,CAACN,KAAK,CAAC,GAAGA,KAAK,CAAC;EAClE,CAAC;EAED,MAAMO,gBAAgB,GAAIP,KAAa,IAAK;IAC1C,OAAO,GAAGA,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGA,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC,GAAG;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,CAAAhB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEiB,WAAW,MAAKpB,SAAS,GAAGgB,UAAU,CAAChB,SAAS,CAACoB,WAAW,CAAC,GAAG,CAAC,CAAC;;EAEhG;EACA,MAAMC,kBAAkB,GAAG;IACzBC,WAAW,EAAE,CAAAnB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEoB,iBAAiB,KAAI,OAAO;IACpDC,kBAAkB,EAAE,CAAArB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsB,4BAA4B,KAAI,IAAI;IACnEN,UAAU,EAAE,CAAAhB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEiB,WAAW,KAAID,UAAU;IAChDO,SAAS,EAAE,CAAAvB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEwB,UAAU,KAAI,CAAC;IACrCC,SAAS,EAAE,CAAC,KAAK;IAAE;IACnBC,gBAAgB,EAAE,CAAC,IAAI;IACvBC,UAAU,EAAE,MAAM;IAClBC,iBAAiB,EAAE,IAAI;IACvBC,WAAW,EAAE,MAAM;IACnBC,kBAAkB,EAAE;EACtB,CAAC;;EAED;EACA,MAAMC,cAAc,GAAG,CAAA/B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEgC,gBAAgB,MAAI5B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6B,GAAG,CAAEC,OAAY,KAAM;IACrFC,MAAM,EAAED,OAAO,CAACE,KAAK,CAACD,MAAM;IAC5BE,IAAI,EAAEH,OAAO,CAACE,KAAK,CAACC,IAAI;IACxB9B,KAAK,EAAEM,UAAU,CAACqB,OAAO,CAACI,aAAa,CAAC;IACxCC,UAAU,EAAG1B,UAAU,CAACqB,OAAO,CAACI,aAAa,CAAC,GAAGtB,UAAU,GAAI,GAAG;IAClEwB,MAAM,EAAEN,OAAO,CAACE,KAAK,CAACI;EACxB,CAAC,CAAC,CAAC,KAAI,EAAE;;EAET;EACA,MAAMC,gBAAgB,GAAG,CAAAzC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE0C,iBAAiB,KAAIX,cAAc,CAACY,MAAM,CAAC,CAACC,GAAwB,EAAEV,OAAY,KAAK;IACzH,MAAMM,MAAM,GAAGN,OAAO,CAACM,MAAM,IAAI,OAAO;IACxC,IAAI,EAAEA,MAAM,IAAII,GAAG,CAAC,EAAE;MACpBA,GAAG,CAACJ,MAAM,CAAC,GAAG;QAAEjC,KAAK,EAAE,CAAC;QAAEgC,UAAU,EAAE;MAAE,CAAC;IAC3C;IACAK,GAAG,CAACJ,MAAM,CAAC,CAACjC,KAAK,IAAI2B,OAAO,CAAC3B,KAAK;IAClCqC,GAAG,CAACJ,MAAM,CAAC,CAACD,UAAU,IAAIL,OAAO,CAACK,UAAU;IAC5C,OAAOK,GAAG;EACZ,CAAC,EAAE,CAAC,CAAwB,CAAC;EAE7B,IAAI,CAACnD,WAAW,EAAE;IAChB,oBACEP,OAAA,CAACzB,GAAG;MAACoF,EAAE,EAAE;QAAEC,QAAQ,EAAE,IAAI;QAAEC,EAAE,EAAE,MAAM;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,eAC5C/D,OAAA,CAACxB,UAAU;QAACwF,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,OAAO;QAAAF,QAAA,EAAC;MAEvC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,IAAIrD,gBAAgB,EAAE;IACpB,oBACEhB,OAAA,CAACzB,GAAG;MAACoF,EAAE,EAAE;QAAEC,QAAQ,EAAE,IAAI;QAAEC,EAAE,EAAE,MAAM;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,eAC5C/D,OAAA,CAACxB,UAAU;QAACwF,OAAO,EAAC,IAAI;QAAAD,QAAA,EAAC;MAEzB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACErE,OAAA,CAAAE,SAAA;IAAA6D,QAAA,gBACE/D,OAAA,CAACN,MAAM;MAAAqE,QAAA,gBACL/D,OAAA;QAAA+D,QAAA,EAAO;MAAsB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrCrE,OAAA;QAAMmD,IAAI,EAAC,aAAa;QAACmB,OAAO,EAAC;MAA8C;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5E,CAAC,eAETrE,OAAA,CAACzB,GAAG;MAACoF,EAAE,EAAE;QAAEC,QAAQ,EAAE,IAAI;QAAEC,EAAE,EAAE,MAAM;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAE5C/D,OAAA,CAACzB,GAAG;QAACgG,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,cAAc,EAAC,eAAe;QAACC,EAAE,EAAE,CAAE;QAAAX,QAAA,gBAC3E/D,OAAA,CAACzB,GAAG;UAACgG,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAAAT,QAAA,gBACrC/D,OAAA,CAACvB,MAAM;YACLkG,SAAS,eAAE3E,OAAA,CAACZ,SAAS;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBO,OAAO,EAAEA,CAAA,KAAMvE,QAAQ,CAAC,eAAeE,WAAW,EAAE,CAAE;YACtDoD,EAAE,EAAE;cAAEkB,EAAE,EAAE;YAAE,CAAE;YAAAd,QAAA,EACf;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrE,OAAA,CAACzB,GAAG;YAAAwF,QAAA,gBACF/D,OAAA,CAACxB,UAAU;cAACwF,OAAO,EAAC,IAAI;cAACc,SAAS,EAAC,IAAI;cAAAf,QAAA,EAAC;YAExC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZ1D,SAAS,iBACRX,OAAA,CAACxB,UAAU;cAACwF,OAAO,EAAC,OAAO;cAACC,KAAK,EAAC,gBAAgB;cAAAF,QAAA,EAC/CpD,SAAS,CAACwC;YAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrE,OAAA,CAACjB,WAAW;UAACgG,IAAI,EAAC,OAAO;UAACpB,EAAE,EAAE;YAAEqB,QAAQ,EAAE;UAAI,CAAE;UAAAjB,QAAA,gBAC9C/D,OAAA,CAAChB,UAAU;YAAA+E,QAAA,EAAC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnCrE,OAAA,CAACf,MAAM;YACLoC,KAAK,EAAEb,SAAU;YACjByE,QAAQ,EAAGC,CAAC,IAAKzE,YAAY,CAACyE,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAE;YAC9C+D,KAAK,EAAC,YAAY;YAAArB,QAAA,gBAElB/D,OAAA,CAACd,QAAQ;cAACmC,KAAK,EAAC,IAAI;cAAA0C,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACrCrE,OAAA,CAACd,QAAQ;cAACmC,KAAK,EAAC,IAAI;cAAA0C,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtCrE,OAAA,CAACd,QAAQ;cAACmC,KAAK,EAAC,IAAI;cAAA0C,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACvCrE,OAAA,CAACd,QAAQ;cAACmC,KAAK,EAAC,IAAI;cAAA0C,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxCrE,OAAA,CAACd,QAAQ;cAACmC,KAAK,EAAC,IAAI;cAAA0C,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxCrE,OAAA,CAACd,QAAQ;cAACmC,KAAK,EAAC,IAAI;cAAA0C,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtCrE,OAAA,CAACd,QAAQ;cAACmC,KAAK,EAAC,KAAK;cAAA0C,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAENrE,OAAA,CAACtB,IAAI;QAAC2G,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAvB,QAAA,gBAEzB/D,OAAA,CAACtB,IAAI;UAAC6G,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAzB,QAAA,eAChB/D,OAAA,CAACnB,KAAK;YAAC8E,EAAE,EAAE;cAAEG,CAAC,EAAE;YAAE,CAAE;YAAAC,QAAA,gBAClB/D,OAAA,CAACxB,UAAU;cAACwF,OAAO,EAAC,IAAI;cAACyB,YAAY;cAAA1B,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrE,OAAA,CAACtB,IAAI;cAAC2G,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAvB,QAAA,gBACzB/D,OAAA,CAACtB,IAAI;gBAAC6G,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACE,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,eAC9B/D,OAAA,CAACrB,IAAI;kBAACqF,OAAO,EAAC,UAAU;kBAAAD,QAAA,eACtB/D,OAAA,CAACpB,WAAW;oBAAAmF,QAAA,gBACV/D,OAAA,CAACzB,GAAG;sBAACgG,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACE,EAAE,EAAE,CAAE;sBAAAX,QAAA,gBAC5C/D,OAAA,CAACP,UAAU;wBAACwE,KAAK,EAAC,SAAS;wBAACN,EAAE,EAAE;0BAAEkB,EAAE,EAAE;wBAAE;sBAAE;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7CrE,OAAA,CAACxB,UAAU;wBAACwF,OAAO,EAAC,OAAO;wBAACC,KAAK,EAAC,gBAAgB;wBAAAF,QAAA,EAAC;sBAEnD;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNrE,OAAA,CAACxB,UAAU;sBAACwF,OAAO,EAAC,IAAI;sBAAC4B,UAAU,EAAC,MAAM;sBAAA7B,QAAA,EACvC3C,cAAc,CAACU,UAAU;oBAAC;sBAAAoC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEPrE,OAAA,CAACtB,IAAI;gBAAC6G,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACE,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,eAC9B/D,OAAA,CAACrB,IAAI;kBAACqF,OAAO,EAAC,UAAU;kBAAAD,QAAA,eACtB/D,OAAA,CAACpB,WAAW;oBAAAmF,QAAA,gBACV/D,OAAA,CAACzB,GAAG;sBAACgG,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACE,EAAE,EAAE,CAAE;sBAAAX,QAAA,gBAC5C/D,OAAA,CAACX,UAAU;wBAAC4E,KAAK,EAAC,SAAS;wBAACN,EAAE,EAAE;0BAAEkB,EAAE,EAAE;wBAAE;sBAAE;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7CrE,OAAA,CAACxB,UAAU;wBAACwF,OAAO,EAAC,OAAO;wBAACC,KAAK,EAAC,gBAAgB;wBAAAF,QAAA,EAAC;sBAEnD;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNrE,OAAA,CAACxB,UAAU;sBAACwF,OAAO,EAAC,IAAI;sBAAC4B,UAAU,EAAC,MAAM;sBAAC3B,KAAK,EAAC,cAAc;sBAAAF,QAAA,EAC5D3C,cAAc,CAACY,kBAAkB,CAACC,WAAW;oBAAC;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC,eACbrE,OAAA,CAACxB,UAAU;sBAACwF,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,cAAc;sBAAAF,QAAA,EAC7CnC,gBAAgB,CAACI,kBAAkB,CAACG,kBAAkB;oBAAC;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEPrE,OAAA,CAACtB,IAAI;gBAAC6G,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACE,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,eAC9B/D,OAAA,CAACrB,IAAI;kBAACqF,OAAO,EAAC,UAAU;kBAAAD,QAAA,eACtB/D,OAAA,CAACpB,WAAW;oBAAAmF,QAAA,gBACV/D,OAAA,CAACzB,GAAG;sBAACgG,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACE,EAAE,EAAE,CAAE;sBAAAX,QAAA,gBAC5C/D,OAAA,CAACV,YAAY;wBAAC2E,KAAK,EAAC,OAAO;wBAACN,EAAE,EAAE;0BAAEkB,EAAE,EAAE;wBAAE;sBAAE;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7CrE,OAAA,CAACxB,UAAU;wBAACwF,OAAO,EAAC,OAAO;wBAACC,KAAK,EAAC,gBAAgB;wBAAAF,QAAA,EAAC;sBAEnD;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNrE,OAAA,CAACxB,UAAU;sBAACwF,OAAO,EAAC,IAAI;sBAAC4B,UAAU,EAAC,MAAM;sBAAC3B,KAAK,EAAC,YAAY;sBAAAF,QAAA,EAC1D3C,cAAc,CAACY,kBAAkB,CAACO,SAAS;oBAAC;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACbrE,OAAA,CAACxB,UAAU;sBAACwF,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,YAAY;sBAAAF,QAAA,EAC3CnC,gBAAgB,CAACI,kBAAkB,CAACQ,gBAAgB;oBAAC;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEPrE,OAAA,CAACtB,IAAI;gBAAC6G,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACE,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,eAC9B/D,OAAA,CAACrB,IAAI;kBAACqF,OAAO,EAAC,UAAU;kBAAAD,QAAA,eACtB/D,OAAA,CAACpB,WAAW;oBAAAmF,QAAA,gBACV/D,OAAA,CAACzB,GAAG;sBAACgG,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACE,EAAE,EAAE,CAAE;sBAAAX,QAAA,gBAC5C/D,OAAA,CAACR,SAAS;wBAACyE,KAAK,EAAC,MAAM;wBAACN,EAAE,EAAE;0BAAEkB,EAAE,EAAE;wBAAE;sBAAE;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACzCrE,OAAA,CAACxB,UAAU;wBAACwF,OAAO,EAAC,OAAO;wBAACC,KAAK,EAAC,gBAAgB;wBAAAF,QAAA,EAAC;sBAEnD;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNrE,OAAA,CAACxB,UAAU;sBAACwF,OAAO,EAAC,IAAI;sBAAC4B,UAAU,EAAC,MAAM;sBAAC3B,KAAK,EAAC,cAAc;sBAAAF,QAAA,EAC5D3C,cAAc,CAACY,kBAAkB,CAACW,WAAW;oBAAC;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC,eACbrE,OAAA,CAACxB,UAAU;sBAACwF,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,cAAc;sBAAAF,QAAA,EAC7CnC,gBAAgB,CAACI,kBAAkB,CAACY,kBAAkB;oBAAC;sBAAAsB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPrE,OAAA,CAACtB,IAAI;UAAC6G,IAAI;UAACC,EAAE,EAAE,EAAG;UAACG,EAAE,EAAE,CAAE;UAAA5B,QAAA,eACvB/D,OAAA,CAACnB,KAAK;YAAC8E,EAAE,EAAE;cAAEG,CAAC,EAAE;YAAE,CAAE;YAAAC,QAAA,gBAClB/D,OAAA,CAACzB,GAAG;cAACgG,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACE,EAAE,EAAE,CAAE;cAAAX,QAAA,gBAC5C/D,OAAA,CAACT,QAAQ;gBAAC0E,KAAK,EAAC,SAAS;gBAACN,EAAE,EAAE;kBAAEkB,EAAE,EAAE;gBAAE;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3CrE,OAAA,CAACxB,UAAU;gBAACwF,OAAO,EAAC,IAAI;gBAAAD,QAAA,EAAC;cAEzB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAELxB,cAAc,CAACgD,MAAM,KAAK,CAAC,gBAC1B7F,OAAA,CAACzB,GAAG;cAACuH,SAAS,EAAC,QAAQ;cAACC,EAAE,EAAE,CAAE;cAAAhC,QAAA,eAC5B/D,OAAA,CAACxB,UAAU;gBAACwF,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAF,QAAA,EAAC;cAEnD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,gBAENrE,OAAA,CAACzB,GAAG;cAAAwF,QAAA,EACDlB,cAAc,CAACmD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACjD,GAAG,CAAC,CAACC,OAAY,EAAEiD,KAAa,kBAC1DjG,OAAA,CAACzB,GAAG;gBAAsBmG,EAAE,EAAE,CAAE;gBAAAX,QAAA,gBAC9B/D,OAAA,CAACzB,GAAG;kBAACgG,OAAO,EAAC,MAAM;kBAACE,cAAc,EAAC,eAAe;kBAACD,UAAU,EAAC,QAAQ;kBAACE,EAAE,EAAE,CAAE;kBAAAX,QAAA,gBAC3E/D,OAAA,CAACzB,GAAG;oBAAAwF,QAAA,gBACF/D,OAAA,CAACxB,UAAU;sBAACwF,OAAO,EAAC,OAAO;sBAAC4B,UAAU,EAAC,QAAQ;sBAAA7B,QAAA,EAC5Cf,OAAO,CAACC;oBAAM;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACbrE,OAAA,CAACxB,UAAU;sBAACwF,OAAO,EAAC,SAAS;sBAACC,KAAK,EAAC,gBAAgB;sBAAAF,QAAA,EACjDf,OAAO,CAACG;oBAAI;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNrE,OAAA,CAACzB,GAAG;oBAACuH,SAAS,EAAC,OAAO;oBAAA/B,QAAA,gBACpB/D,OAAA,CAACxB,UAAU;sBAACwF,OAAO,EAAC,OAAO;sBAAC4B,UAAU,EAAC,QAAQ;sBAAA7B,QAAA,GAC5Cf,OAAO,CAACK,UAAU,CAACxB,OAAO,CAAC,CAAC,CAAC,EAAC,GACjC;oBAAA;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbrE,OAAA,CAACxB,UAAU;sBAACwF,OAAO,EAAC,SAAS;sBAACC,KAAK,EAAC,gBAAgB;sBAAAF,QAAA,EACjD3C,cAAc,CAAC4B,OAAO,CAAC3B,KAAK;oBAAC;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrE,OAAA,CAACb,cAAc;kBACb6E,OAAO,EAAC,aAAa;kBACrB3C,KAAK,EAAE2B,OAAO,CAACK,UAAW;kBAC1BM,EAAE,EAAE;oBAAEuC,MAAM,EAAE,CAAC;oBAAEC,YAAY,EAAE;kBAAE;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA,GAvBMrB,OAAO,CAACC,MAAM;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwBnB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPrE,OAAA,CAACtB,IAAI;UAAC6G,IAAI;UAACC,EAAE,EAAE,EAAG;UAACG,EAAE,EAAE,CAAE;UAAA5B,QAAA,eACvB/D,OAAA,CAACnB,KAAK;YAAC8E,EAAE,EAAE;cAAEG,CAAC,EAAE;YAAE,CAAE;YAAAC,QAAA,gBAClB/D,OAAA,CAACxB,UAAU;cAACwF,OAAO,EAAC,IAAI;cAACyB,YAAY;cAAA1B,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAEZ+B,MAAM,CAACC,IAAI,CAAC9C,gBAAgB,CAAC,CAACsC,MAAM,KAAK,CAAC,gBACzC7F,OAAA,CAACzB,GAAG;cAACuH,SAAS,EAAC,QAAQ;cAACC,EAAE,EAAE,CAAE;cAAAhC,QAAA,eAC5B/D,OAAA,CAACxB,UAAU;gBAACwF,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAF,QAAA,EAAC;cAEnD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,gBAENrE,OAAA,CAACzB,GAAG;cAAAwF,QAAA,EACDqC,MAAM,CAACE,OAAO,CAAC/C,gBAAgB,CAAC,CAACR,GAAG,CAAC,CAAC,CAACO,MAAM,EAAE5C,IAAI,CAAgB,kBAClEV,OAAA,CAACzB,GAAG;gBAAcmG,EAAE,EAAE,CAAE;gBAAAX,QAAA,gBACtB/D,OAAA,CAACzB,GAAG;kBAACgG,OAAO,EAAC,MAAM;kBAACE,cAAc,EAAC,eAAe;kBAACD,UAAU,EAAC,QAAQ;kBAACE,EAAE,EAAE,CAAE;kBAAAX,QAAA,gBAC3E/D,OAAA,CAACxB,UAAU;oBAACwF,OAAO,EAAC,OAAO;oBAAC4B,UAAU,EAAC,QAAQ;oBAAA7B,QAAA,EAC5CT;kBAAM;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACbrE,OAAA,CAACzB,GAAG;oBAACuH,SAAS,EAAC,OAAO;oBAAA/B,QAAA,gBACpB/D,OAAA,CAACxB,UAAU;sBAACwF,OAAO,EAAC,OAAO;sBAAC4B,UAAU,EAAC,QAAQ;sBAAA7B,QAAA,GAC5CrD,IAAI,CAAC2C,UAAU,CAACxB,OAAO,CAAC,CAAC,CAAC,EAAC,GAC9B;oBAAA;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbrE,OAAA,CAACxB,UAAU;sBAACwF,OAAO,EAAC,SAAS;sBAACC,KAAK,EAAC,gBAAgB;sBAAAF,QAAA,EACjD3C,cAAc,CAACV,IAAI,CAACW,KAAK;oBAAC;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrE,OAAA,CAACb,cAAc;kBACb6E,OAAO,EAAC,aAAa;kBACrB3C,KAAK,EAAEX,IAAI,CAAC2C,UAAW;kBACvBM,EAAE,EAAE;oBAAEuC,MAAM,EAAE,CAAC;oBAAEC,YAAY,EAAE;kBAAE;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA,GAlBMf,MAAM;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmBX,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPrE,OAAA,CAACtB,IAAI;UAAC6G,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAzB,QAAA,eAChB/D,OAAA,CAACnB,KAAK;YAAC8E,EAAE,EAAE;cAAEG,CAAC,EAAE;YAAE,CAAE;YAAAC,QAAA,gBAClB/D,OAAA,CAACxB,UAAU;cAACwF,OAAO,EAAC,IAAI;cAACyB,YAAY;cAAA1B,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrE,OAAA,CAACzB,GAAG;cACFoF,EAAE,EAAE;gBACFuC,MAAM,EAAE,GAAG;gBACX3B,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB8B,OAAO,EAAE,oBAAoB;gBAC7BJ,YAAY,EAAE;cAChB,CAAE;cAAApC,QAAA,eAEF/D,OAAA,CAACzB,GAAG;gBAACuH,SAAS,EAAC,QAAQ;gBAAA/B,QAAA,gBACrB/D,OAAA,CAACR,SAAS;kBAACmE,EAAE,EAAE;oBAAE6C,QAAQ,EAAE,EAAE;oBAAEvC,KAAK,EAAE,gBAAgB;oBAAES,EAAE,EAAE;kBAAE;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnErE,OAAA,CAACxB,UAAU;kBAACwF,OAAO,EAAC,IAAI;kBAACC,KAAK,EAAC,gBAAgB;kBAACwB,YAAY;kBAAA1B,QAAA,EAAC;gBAE7D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrE,OAAA,CAACxB,UAAU;kBAACwF,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,EAAC;gBAEnD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrE,OAAA,CAAClB,IAAI;kBAACsG,KAAK,EAAC,aAAa;kBAACnB,KAAK,EAAC,SAAS;kBAACc,IAAI,EAAC,OAAO;kBAACpB,EAAE,EAAE;oBAAE8C,EAAE,EAAE;kBAAE;gBAAE;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACjE,EAAA,CAtWID,aAAuB;EAAA,QACVR,WAAW,EACAC,SAAS,EAKTC,QAAQ,EASqBA,QAAQ,EAStCA,QAAQ;AAAA;AAAA6G,EAAA,GAzB/BvG,aAAuB;AAwW7B,eAAeA,aAAa;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}