#!/usr/bin/env python3

"""
TrustVault - Test Runner Sc<PERSON>t

This script runs all tests for the TrustVault application and provides
a comprehensive test report.
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_header(title):
    """Print a formatted header."""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_section(title):
    """Print a formatted section header."""
    print(f"\n📋 {title}")
    print("-" * 40)

def run_command(command, cwd=None, description=""):
    """Run a command and return the result."""
    if description:
        print(f"🔄 {description}...")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            timeout=300  # 5 minutes timeout
        )
        
        if result.returncode == 0:
            print(f"✅ Success: {description}")
            return True, result.stdout
        else:
            print(f"❌ Failed: {description}")
            print(f"Error: {result.stderr}")
            return False, result.stderr
    
    except subprocess.TimeoutExpired:
        print(f"⏰ Timeout: {description}")
        return False, "Command timed out"
    except Exception as e:
        print(f"💥 Exception: {description} - {str(e)}")
        return False, str(e)

def check_prerequisites():
    """Check if all prerequisites are installed."""
    print_section("Checking Prerequisites")
    
    prerequisites = [
        ("python", "python --version", "Python"),
        ("docker", "docker --version", "Docker"),
        ("docker-compose", "docker-compose --version", "Docker Compose"),
    ]
    
    all_good = True
    
    for cmd, check_cmd, name in prerequisites:
        success, output = run_command(check_cmd, description=f"Checking {name}")
        if success:
            version = output.strip().split('\n')[0]
            print(f"  ✅ {name}: {version}")
        else:
            print(f"  ❌ {name}: Not found or not working")
            all_good = False
    
    return all_good

def run_backend_tests():
    """Run Django backend tests."""
    print_section("Running Backend Tests")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found")
        return False
    
    # Check if we're in a Docker environment or local development
    if os.path.exists("docker-compose.yml"):
        print("🐳 Running tests in Docker environment...")
        
        # Start test database
        success, _ = run_command(
            "docker-compose -f docker-compose.dev.yml up -d postgres redis",
            description="Starting test dependencies"
        )
        
        if not success:
            return False
        
        # Wait for database to be ready
        time.sleep(5)
        
        # Run tests in Docker
        success, output = run_command(
            "docker-compose -f docker-compose.dev.yml exec -T backend python manage.py test --verbosity=2",
            description="Running Django tests in Docker"
        )
        
        print(output)
        return success
    
    else:
        print("🏠 Running tests in local environment...")
        
        # Run tests locally
        success, output = run_command(
            "python manage.py test --verbosity=2",
            cwd=backend_dir,
            description="Running Django tests locally"
        )
        
        print(output)
        return success

def run_frontend_tests():
    """Run React frontend tests."""
    print_section("Running Frontend Tests")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ Frontend directory not found")
        return False
    
    # Check if node_modules exists
    if not (frontend_dir / "node_modules").exists():
        print("📦 Installing frontend dependencies...")
        success, _ = run_command(
            "npm install",
            cwd=frontend_dir,
            description="Installing npm dependencies"
        )
        
        if not success:
            return False
    
    # Run frontend tests
    success, output = run_command(
        "npm test -- --coverage --watchAll=false",
        cwd=frontend_dir,
        description="Running React tests"
    )
    
    print(output)
    return success

def run_integration_tests():
    """Run integration tests."""
    print_section("Running Integration Tests")
    
    if not os.path.exists("docker-compose.yml"):
        print("⚠️  Docker Compose not found, skipping integration tests")
        return True
    
    # Start all services
    success, _ = run_command(
        "docker-compose -f docker-compose.dev.yml up -d",
        description="Starting all services for integration tests"
    )
    
    if not success:
        return False
    
    # Wait for services to be ready
    print("⏳ Waiting for services to be ready...")
    time.sleep(30)
    
    # Test backend health
    success, _ = run_command(
        "curl -f http://localhost:8000/health/",
        description="Testing backend health endpoint"
    )
    
    if not success:
        print("❌ Backend health check failed")
        return False
    
    # Test frontend health
    success, _ = run_command(
        "curl -f http://localhost:3000/health",
        description="Testing frontend health endpoint"
    )
    
    if not success:
        print("⚠️  Frontend health check failed (this might be expected)")
    
    print("✅ Integration tests completed")
    return True

def generate_test_report():
    """Generate a test report."""
    print_section("Test Report Summary")
    
    report = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "backend_tests": False,
        "frontend_tests": False,
        "integration_tests": False,
    }
    
    # This would be populated by the actual test results
    # For now, we'll just show the structure
    
    print(f"📊 Test Report Generated at: {report['timestamp']}")
    print(f"🔧 Backend Tests: {'✅ PASSED' if report['backend_tests'] else '❌ FAILED'}")
    print(f"🖥️  Frontend Tests: {'✅ PASSED' if report['frontend_tests'] else '❌ FAILED'}")
    print(f"🔗 Integration Tests: {'✅ PASSED' if report['integration_tests'] else '❌ FAILED'}")

def main():
    """Main test runner function."""
    print_header("🧪 TrustVault Test Suite")
    
    start_time = time.time()
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites check failed. Please install missing dependencies.")
        sys.exit(1)
    
    # Run tests
    backend_success = run_backend_tests()
    frontend_success = run_frontend_tests()
    integration_success = run_integration_tests()
    
    # Generate report
    generate_test_report()
    
    # Summary
    end_time = time.time()
    duration = end_time - start_time
    
    print_header("🎯 Test Results Summary")
    print(f"⏱️  Total Duration: {duration:.2f} seconds")
    print(f"🔧 Backend Tests: {'✅ PASSED' if backend_success else '❌ FAILED'}")
    print(f"🖥️  Frontend Tests: {'✅ PASSED' if frontend_success else '❌ FAILED'}")
    print(f"🔗 Integration Tests: {'✅ PASSED' if integration_success else '❌ FAILED'}")
    
    overall_success = backend_success and frontend_success and integration_success
    
    if overall_success:
        print("\n🎉 All tests passed! TrustVault is ready to go!")
        sys.exit(0)
    else:
        print("\n⚠️  Some tests failed. Please review the output above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
