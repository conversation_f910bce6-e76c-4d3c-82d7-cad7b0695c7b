# 📚 TrustVault API Documentation

This document provides comprehensive information about the TrustVault REST API endpoints, authentication, and usage examples.

## 🔗 Base URL

- **Development**: `http://localhost:8000/api/v1`
- **Production**: `https://your-domain.com/api/v1`

## 🔐 Authentication

TrustVault uses JWT (JSON Web Tokens) for authentication. All protected endpoints require a valid JWT token in the Authorization header.

### Authentication Flow

1. **Login** to get access and refresh tokens
2. **Use access token** for API requests
3. **Refresh token** when access token expires
4. **Logout** to invalidate tokens

### Headers
```http
Authorization: Bearer <your-access-token>
Content-Type: application/json
```

## 📋 API Endpoints

### 🔑 Authentication Endpoints

#### Register User
```http
POST /auth/register/
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "username",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "password": "secure_password",
  "password_confirm": "secure_password"
}
```

**Response (201 Created):**
```json
{
  "message": "User registered successfully",
  "user_id": "uuid-here"
}
```

#### Login
```http
POST /auth/login/
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "secure_password",
  "mfa_token": "123456"  // Optional, required if MFA is enabled
}
```

**Response (200 OK):**
```json
{
  "access": "jwt-access-token",
  "refresh": "jwt-refresh-token",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "username": "username",
    "first_name": "John",
    "last_name": "Doe",
    "is_mfa_enabled": false
  }
}
```

#### Refresh Token
```http
POST /auth/token/refresh/
```

**Request Body:**
```json
{
  "refresh": "jwt-refresh-token"
}
```

#### Logout
```http
POST /auth/logout/
```

**Request Body:**
```json
{
  "refresh_token": "jwt-refresh-token"
}
```

#### Get User Profile
```http
GET /auth/profile/
```

**Response (200 OK):**
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "username": "username",
  "first_name": "John",
  "last_name": "Doe",
  "is_mfa_enabled": false,
  "date_joined": "2024-01-01T00:00:00Z",
  "last_login": "2024-01-01T12:00:00Z"
}
```

### 🔒 Multi-Factor Authentication (MFA)

#### Setup MFA
```http
POST /auth/mfa/setup/
```

**Response (200 OK):**
```json
{
  "qr_code_url": "otpauth://totp/TrustVault:<EMAIL>?secret=...",
  "secret_key": "SECRET_KEY_HERE",
  "message": "Scan the QR code with your authenticator app"
}
```

#### Verify MFA Setup
```http
POST /auth/mfa/verify/
```

**Request Body:**
```json
{
  "token": "123456"
}
```

#### Disable MFA
```http
POST /auth/mfa/disable/
```

### 📊 Portfolio Management

#### List Portfolios
```http
GET /portfolio/
```

**Response (200 OK):**
```json
[
  {
    "id": "uuid",
    "name": "My Portfolio",
    "description": "Investment portfolio",
    "currency": "USD",
    "total_value": "10000.00",
    "created_at": "2024-01-01T00:00:00Z",
    "holdings_count": 5
  }
]
```

#### Create Portfolio
```http
POST /portfolio/
```

**Request Body:**
```json
{
  "name": "New Portfolio",
  "description": "My investment portfolio",
  "currency": "USD"
}
```

#### Get Portfolio Details
```http
GET /portfolio/{portfolio_id}/
```

**Response (200 OK):**
```json
{
  "id": "uuid",
  "name": "My Portfolio",
  "description": "Investment portfolio",
  "currency": "USD",
  "total_value": "10000.00",
  "total_cost": "9500.00",
  "profit_loss": "500.00",
  "profit_loss_percentage": "5.26",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-02T00:00:00Z"
}
```

#### Get Portfolio Analytics
```http
GET /portfolio/{portfolio_id}/analytics/
```

**Response (200 OK):**
```json
{
  "total_value": "10000.00",
  "total_cost": "9500.00",
  "total_profit_loss": "500.00",
  "total_profit_loss_percentage": "5.26",
  "asset_allocation": [
    {
      "symbol": "AAPL",
      "name": "Apple Inc.",
      "value": "5000.00",
      "percentage": "50.00",
      "quantity": "33.33"
    }
  ],
  "sector_allocation": [
    {
      "sector": "Technology",
      "value": "7000.00",
      "percentage": "70.00",
      "count": 3
    }
  ],
  "top_performers": [
    {
      "symbol": "AAPL",
      "name": "Apple Inc.",
      "profit_loss": "300.00",
      "profit_loss_percentage": "6.38",
      "current_value": "5000.00"
    }
  ],
  "worst_performers": []
}
```

### 💼 Holdings Management

#### List Holdings
```http
GET /portfolio/{portfolio_id}/holdings/
```

#### Create Holding
```http
POST /portfolio/{portfolio_id}/holdings/
```

**Request Body:**
```json
{
  "asset_id": "uuid",
  "quantity": "10.00",
  "average_cost": "150.00"
}
```

### 💰 Transaction Management

#### List Transactions
```http
GET /portfolio/{portfolio_id}/transactions/
```

**Query Parameters:**
- `transaction_type`: Filter by type (BUY, SELL, DIVIDEND, etc.)
- `asset_id`: Filter by asset
- `page`: Page number for pagination

#### Create Transaction
```http
POST /portfolio/{portfolio_id}/transactions/
```

**Request Body:**
```json
{
  "asset_id": "uuid",
  "transaction_type": "BUY",
  "quantity": "10.00",
  "price": "150.00",
  "fees": "5.00",
  "notes": "Purchase notes"
}
```

### 🏦 Asset Management

#### List Assets
```http
GET /portfolio/assets/
```

**Query Parameters:**
- `type`: Filter by asset type (STOCK, BOND, CRYPTO, etc.)
- `search`: Search by name or symbol
- `sector`: Filter by sector

**Response (200 OK):**
```json
[
  {
    "id": "uuid",
    "symbol": "AAPL",
    "name": "Apple Inc.",
    "asset_type": "STOCK",
    "sector": "Technology",
    "current_price": "150.00",
    "currency": "USD",
    "last_updated": "2024-01-01T12:00:00Z"
  }
]
```

### 🛡️ Security Endpoints

#### Security Dashboard
```http
GET /security/dashboard/
```

**Response (200 OK):**
```json
{
  "security_score": 85,
  "active_sessions": 2,
  "recent_events": 5,
  "mfa_enabled": true,
  "last_login": "2024-01-01T12:00:00Z",
  "failed_login_attempts": 0
}
```

#### Security Events
```http
GET /security/events/
```

**Query Parameters:**
- `risk_level`: Filter by risk level (LOW, MEDIUM, HIGH, CRITICAL)
- `event_type`: Filter by event type
- `is_resolved`: Filter by resolution status

#### Audit Logs
```http
GET /security/audit-logs/
```

**Query Parameters:**
- `action`: Filter by action type
- `resource_type`: Filter by resource type
- `severity`: Filter by severity level

### 🏥 System Health

#### Health Check
```http
GET /core/status/
```

**Response (200 OK):**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "1.0.0",
  "database": "connected",
  "redis": "connected"
}
```

## 📝 Response Formats

### Success Response
```json
{
  "data": { ... },
  "message": "Success message",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Error Response
```json
{
  "error": "Error message",
  "details": { ... },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Validation Error Response
```json
{
  "error": "Validation failed",
  "details": {
    "field_name": ["Error message for this field"]
  }
}
```

## 📊 Pagination

List endpoints support pagination:

**Query Parameters:**
- `page`: Page number (default: 1)
- `page_size`: Items per page (default: 20, max: 100)

**Response Format:**
```json
{
  "count": 100,
  "next": "http://api.example.com/endpoint/?page=3",
  "previous": "http://api.example.com/endpoint/?page=1",
  "results": [ ... ]
}
```

## 🔒 Rate Limiting

API endpoints are rate-limited to prevent abuse:

- **Authentication endpoints**: 5 requests per minute
- **General endpoints**: 100 requests per minute
- **Bulk operations**: 10 requests per minute

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## 🧪 Testing the API

### Using cURL

```bash
# Login
curl -X POST http://localhost:8000/api/v1/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "admin123"}'

# Get portfolios
curl -X GET http://localhost:8000/api/v1/portfolio/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Using Python Requests

```python
import requests

# Login
response = requests.post('http://localhost:8000/api/v1/auth/login/', json={
    'email': '<EMAIL>',
    'password': 'admin123'
})

token = response.json()['access']

# Get portfolios
headers = {'Authorization': f'Bearer {token}'}
portfolios = requests.get('http://localhost:8000/api/v1/portfolio/', headers=headers)
```

## 📖 Interactive Documentation

Visit these URLs for interactive API documentation:

- **Swagger UI**: http://localhost:8000/api/docs/
- **ReDoc**: http://localhost:8000/api/redoc/
- **OpenAPI Schema**: http://localhost:8000/api/schema/

---

**For more information, visit the [TrustVault Documentation](README.md)**
