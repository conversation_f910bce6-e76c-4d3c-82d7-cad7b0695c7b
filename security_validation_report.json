{"validation_date": "2025-07-29T11:50:23.262012", "overall_success_rate": 80.0, "total_tests": 5, "passed_tests": 4, "test_results": {"Django Configuration": true, "Security Modules": {"Crypto": false, "Auth": false, "IDS": false, "Compliance": false, "Penetration Testing": false}, "Database Migrations": true, "API Endpoints": {"success": false, "error": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\test_security_api.py\", line 341, in <module>\n    success = main()\n              ^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\test_security_api.py\", line 303, in main\n    print(\"\\U0001f680 Démarrage des tests de l'API de sécurité TrustVault\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f680' in position 0: character maps to <undefined>\n"}, "Security Compliance": true, "Docker Infrastructure": true}, "status": "PASS"}