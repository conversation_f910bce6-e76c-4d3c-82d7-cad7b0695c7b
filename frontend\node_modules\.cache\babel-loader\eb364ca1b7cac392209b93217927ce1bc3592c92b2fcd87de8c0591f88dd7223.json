{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\components\\\\ErrorBoundary\\\\ErrorBoundary.tsx\";\n// TrustVault - Error Boundary Component\n\nimport React, { Component } from 'react';\nimport { Box, Typography, Button, Card, CardContent, Alert, Container } from '@mui/material';\nimport { ErrorOutline, Refresh, Home, BugReport } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ErrorBoundary extends Component {\n  constructor(props) {\n    super(props);\n    this.handleReload = () => {\n      window.location.reload();\n    };\n    this.handleGoHome = () => {\n      window.location.href = '/';\n    };\n    this.handleReportError = () => {\n      const {\n        error,\n        errorInfo\n      } = this.state;\n      const errorReport = {\n        error: error === null || error === void 0 ? void 0 : error.toString(),\n        stack: error === null || error === void 0 ? void 0 : error.stack,\n        componentStack: errorInfo === null || errorInfo === void 0 ? void 0 : errorInfo.componentStack,\n        timestamp: new Date().toISOString(),\n        userAgent: navigator.userAgent,\n        url: window.location.href\n      };\n\n      // Copy error report to clipboard\n      navigator.clipboard.writeText(JSON.stringify(errorReport, null, 2)).then(() => {\n        alert('Error report copied to clipboard. Please share this with the development team.');\n      }).catch(() => {\n        console.log('Error report:', errorReport);\n        alert('Error report logged to console. Please check the browser console and share the details with the development team.');\n      });\n    };\n    this.state = {\n      hasError: false\n    };\n  }\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return {\n      hasError: true,\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    // Log the error to console and potentially to a logging service\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    this.setState({\n      error,\n      errorInfo\n    });\n\n    // You can also log the error to an error reporting service here\n    // Example: logErrorToService(error, errorInfo);\n  }\n  render() {\n    if (this.state.hasError) {\n      var _this$state$error;\n      // Custom fallback UI\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      // Default error UI\n      return /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"md\",\n        sx: {\n          mt: 8\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              p: 4,\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(ErrorOutline, {\n              sx: {\n                fontSize: 80,\n                color: 'error.main',\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              component: \"h1\",\n              gutterBottom: true,\n              children: \"Oops! Something went wrong\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              color: \"text.secondary\",\n              paragraph: true,\n              children: \"We're sorry, but something unexpected happened. The error has been logged and our team will investigate the issue.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 3,\n                textAlign: 'left'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                children: \"Error Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                component: \"pre\",\n                sx: {\n                  fontSize: '0.875rem'\n                },\n                children: (_this$state$error = this.state.error) === null || _this$state$error === void 0 ? void 0 : _this$state$error.toString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 2,\n                justifyContent: 'center',\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                startIcon: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 30\n                }, this),\n                onClick: this.handleReload,\n                children: \"Reload Page\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                startIcon: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 30\n                }, this),\n                onClick: this.handleGoHome,\n                children: \"Go Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"error\",\n                startIcon: /*#__PURE__*/_jsxDEV(BugReport, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 30\n                }, this),\n                onClick: this.handleReportError,\n                children: \"Report Error\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), process.env.NODE_ENV === 'development' && this.state.errorInfo && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 4,\n                textAlign: 'left'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Component Stack (Development Only):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"info\",\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  component: \"pre\",\n                  sx: {\n                    fontSize: '0.75rem'\n                  },\n                  children: this.state.errorInfo.componentStack\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nexport default ErrorBoundary;\n\n// Higher-order component for easier usage\nexport const withErrorBoundary = (Component, fallback) => {\n  const WrappedComponent = props => /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    fallback: fallback,\n    children: /*#__PURE__*/_jsxDEV(Component, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 5\n  }, this);\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\n  return WrappedComponent;\n};\n\n// Hook for error reporting in functional components\nexport const useErrorHandler = () => {\n  const handleError = (error, errorInfo) => {\n    console.error('Error caught by useErrorHandler:', error, errorInfo);\n\n    // You can integrate with error reporting services here\n    // Example: reportError(error, errorInfo);\n  };\n  return handleError;\n};", "map": {"version": 3, "names": ["React", "Component", "Box", "Typography", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Container", "ErrorOutline", "Refresh", "Home", "BugReport", "jsxDEV", "_jsxDEV", "Error<PERSON>ou<PERSON><PERSON>", "constructor", "props", "handleReload", "window", "location", "reload", "handleGoHome", "href", "handleReportError", "error", "errorInfo", "state", "errorReport", "toString", "stack", "componentStack", "timestamp", "Date", "toISOString", "userAgent", "navigator", "url", "clipboard", "writeText", "JSON", "stringify", "then", "alert", "catch", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "componentDidCatch", "setState", "render", "_this$state$error", "fallback", "max<PERSON><PERSON><PERSON>", "sx", "mt", "children", "p", "textAlign", "fontSize", "color", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "component", "gutterBottom", "paragraph", "severity", "display", "gap", "justifyContent", "flexWrap", "startIcon", "onClick", "process", "env", "NODE_ENV", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WrappedComponent", "displayName", "name", "useErrorHandler", "handleError"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/components/ErrorBoundary/ErrorBoundary.tsx"], "sourcesContent": ["// TrustVault - Error Boundary Component\n\nimport React, { Component, ErrorInfo, ReactNode } from 'react';\nimport {\n  Box,\n  Typography,\n  <PERSON><PERSON>,\n  Card,\n  CardContent,\n  Alert,\n  Container,\n} from '@mui/material';\nimport {\n  ErrorOutline,\n  Refresh,\n  Home,\n  BugReport,\n} from '@mui/icons-material';\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n  errorInfo?: ErrorInfo;\n}\n\nclass ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    // Update state so the next render will show the fallback UI\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    // Log the error to console and potentially to a logging service\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    \n    this.setState({\n      error,\n      errorInfo,\n    });\n\n    // You can also log the error to an error reporting service here\n    // Example: logErrorToService(error, errorInfo);\n  }\n\n  handleReload = () => {\n    window.location.reload();\n  };\n\n  handleGoHome = () => {\n    window.location.href = '/';\n  };\n\n  handleReportError = () => {\n    const { error, errorInfo } = this.state;\n    const errorReport = {\n      error: error?.toString(),\n      stack: error?.stack,\n      componentStack: errorInfo?.componentStack,\n      timestamp: new Date().toISOString(),\n      userAgent: navigator.userAgent,\n      url: window.location.href,\n    };\n\n    // Copy error report to clipboard\n    navigator.clipboard.writeText(JSON.stringify(errorReport, null, 2))\n      .then(() => {\n        alert('Error report copied to clipboard. Please share this with the development team.');\n      })\n      .catch(() => {\n        console.log('Error report:', errorReport);\n        alert('Error report logged to console. Please check the browser console and share the details with the development team.');\n      });\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // Custom fallback UI\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      // Default error UI\n      return (\n        <Container maxWidth=\"md\" sx={{ mt: 8 }}>\n          <Card>\n            <CardContent sx={{ p: 4, textAlign: 'center' }}>\n              <ErrorOutline\n                sx={{\n                  fontSize: 80,\n                  color: 'error.main',\n                  mb: 2,\n                }}\n              />\n              \n              <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n                Oops! Something went wrong\n              </Typography>\n              \n              <Typography variant=\"body1\" color=\"text.secondary\" paragraph>\n                We're sorry, but something unexpected happened. The error has been logged\n                and our team will investigate the issue.\n              </Typography>\n\n              <Alert severity=\"error\" sx={{ mb: 3, textAlign: 'left' }}>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  Error Details:\n                </Typography>\n                <Typography variant=\"body2\" component=\"pre\" sx={{ fontSize: '0.875rem' }}>\n                  {this.state.error?.toString()}\n                </Typography>\n              </Alert>\n\n              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>\n                <Button\n                  variant=\"contained\"\n                  startIcon={<Refresh />}\n                  onClick={this.handleReload}\n                >\n                  Reload Page\n                </Button>\n                \n                <Button\n                  variant=\"outlined\"\n                  startIcon={<Home />}\n                  onClick={this.handleGoHome}\n                >\n                  Go Home\n                </Button>\n                \n                <Button\n                  variant=\"outlined\"\n                  color=\"error\"\n                  startIcon={<BugReport />}\n                  onClick={this.handleReportError}\n                >\n                  Report Error\n                </Button>\n              </Box>\n\n              {process.env.NODE_ENV === 'development' && this.state.errorInfo && (\n                <Box sx={{ mt: 4, textAlign: 'left' }}>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Component Stack (Development Only):\n                  </Typography>\n                  <Alert severity=\"info\">\n                    <Typography variant=\"body2\" component=\"pre\" sx={{ fontSize: '0.75rem' }}>\n                      {this.state.errorInfo.componentStack}\n                    </Typography>\n                  </Alert>\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </Container>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n\n// Higher-order component for easier usage\nexport const withErrorBoundary = <P extends object>(\n  Component: React.ComponentType<P>,\n  fallback?: ReactNode\n) => {\n  const WrappedComponent = (props: P) => (\n    <ErrorBoundary fallback={fallback}>\n      <Component {...props} />\n    </ErrorBoundary>\n  );\n\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\n  \n  return WrappedComponent;\n};\n\n// Hook for error reporting in functional components\nexport const useErrorHandler = () => {\n  const handleError = (error: Error, errorInfo?: any) => {\n    console.error('Error caught by useErrorHandler:', error, errorInfo);\n    \n    // You can integrate with error reporting services here\n    // Example: reportError(error, errorInfo);\n  };\n\n  return handleError;\n};\n"], "mappings": ";AAAA;;AAEA,OAAOA,KAAK,IAAIC,SAAS,QAA8B,OAAO;AAC9D,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,SAAS,QACJ,eAAe;AACtB,SACEC,YAAY,EACZC,OAAO,EACPC,IAAI,EACJC,SAAS,QACJ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAa7B,MAAMC,aAAa,SAASd,SAAS,CAAe;EAClDe,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;IAAC,KAsBfC,YAAY,GAAG,MAAM;MACnBC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAAA,KAEDC,YAAY,GAAG,MAAM;MACnBH,MAAM,CAACC,QAAQ,CAACG,IAAI,GAAG,GAAG;IAC5B,CAAC;IAAA,KAEDC,iBAAiB,GAAG,MAAM;MACxB,MAAM;QAAEC,KAAK;QAAEC;MAAU,CAAC,GAAG,IAAI,CAACC,KAAK;MACvC,MAAMC,WAAW,GAAG;QAClBH,KAAK,EAAEA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,QAAQ,CAAC,CAAC;QACxBC,KAAK,EAAEL,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEK,KAAK;QACnBC,cAAc,EAAEL,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEK,cAAc;QACzCC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAEC,SAAS,CAACD,SAAS;QAC9BE,GAAG,EAAElB,MAAM,CAACC,QAAQ,CAACG;MACvB,CAAC;;MAED;MACAa,SAAS,CAACE,SAAS,CAACC,SAAS,CAACC,IAAI,CAACC,SAAS,CAACb,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAChEc,IAAI,CAAC,MAAM;QACVC,KAAK,CAAC,gFAAgF,CAAC;MACzF,CAAC,CAAC,CACDC,KAAK,CAAC,MAAM;QACXC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAElB,WAAW,CAAC;QACzCe,KAAK,CAAC,mHAAmH,CAAC;MAC5H,CAAC,CAAC;IACN,CAAC;IAjDC,IAAI,CAAChB,KAAK,GAAG;MAAEoB,QAAQ,EAAE;IAAM,CAAC;EAClC;EAEA,OAAOC,wBAAwBA,CAACvB,KAAY,EAAS;IACnD;IACA,OAAO;MAAEsB,QAAQ,EAAE,IAAI;MAAEtB;IAAM,CAAC;EAClC;EAEAwB,iBAAiBA,CAACxB,KAAY,EAAEC,SAAoB,EAAE;IACpD;IACAmB,OAAO,CAACpB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,EAAEC,SAAS,CAAC;IAEjE,IAAI,CAACwB,QAAQ,CAAC;MACZzB,KAAK;MACLC;IACF,CAAC,CAAC;;IAEF;IACA;EACF;EAgCAyB,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACxB,KAAK,CAACoB,QAAQ,EAAE;MAAA,IAAAK,iBAAA;MACvB;MACA,IAAI,IAAI,CAACnC,KAAK,CAACoC,QAAQ,EAAE;QACvB,OAAO,IAAI,CAACpC,KAAK,CAACoC,QAAQ;MAC5B;;MAEA;MACA,oBACEvC,OAAA,CAACN,SAAS;QAAC8C,QAAQ,EAAC,IAAI;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,eACrC3C,OAAA,CAACT,IAAI;UAAAoD,QAAA,eACH3C,OAAA,CAACR,WAAW;YAACiD,EAAE,EAAE;cAAEG,CAAC,EAAE,CAAC;cAAEC,SAAS,EAAE;YAAS,CAAE;YAAAF,QAAA,gBAC7C3C,OAAA,CAACL,YAAY;cACX8C,EAAE,EAAE;gBACFK,QAAQ,EAAE,EAAE;gBACZC,KAAK,EAAE,YAAY;gBACnBC,EAAE,EAAE;cACN;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEFpD,OAAA,CAACX,UAAU;cAACgE,OAAO,EAAC,IAAI;cAACC,SAAS,EAAC,IAAI;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAErD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbpD,OAAA,CAACX,UAAU;cAACgE,OAAO,EAAC,OAAO;cAACN,KAAK,EAAC,gBAAgB;cAACS,SAAS;cAAAb,QAAA,EAAC;YAG7D;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbpD,OAAA,CAACP,KAAK;cAACgE,QAAQ,EAAC,OAAO;cAAChB,EAAE,EAAE;gBAAEO,EAAE,EAAE,CAAC;gBAAEH,SAAS,EAAE;cAAO,CAAE;cAAAF,QAAA,gBACvD3C,OAAA,CAACX,UAAU;gBAACgE,OAAO,EAAC,WAAW;gBAACE,YAAY;gBAAAZ,QAAA,EAAC;cAE7C;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbpD,OAAA,CAACX,UAAU;gBAACgE,OAAO,EAAC,OAAO;gBAACC,SAAS,EAAC,KAAK;gBAACb,EAAE,EAAE;kBAAEK,QAAQ,EAAE;gBAAW,CAAE;gBAAAH,QAAA,GAAAL,iBAAA,GACtE,IAAI,CAACzB,KAAK,CAACF,KAAK,cAAA2B,iBAAA,uBAAhBA,iBAAA,CAAkBvB,QAAQ,CAAC;cAAC;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAERpD,OAAA,CAACZ,GAAG;cAACqD,EAAE,EAAE;gBAAEiB,OAAO,EAAE,MAAM;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,cAAc,EAAE,QAAQ;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAlB,QAAA,gBAC/E3C,OAAA,CAACV,MAAM;gBACL+D,OAAO,EAAC,WAAW;gBACnBS,SAAS,eAAE9D,OAAA,CAACJ,OAAO;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBW,OAAO,EAAE,IAAI,CAAC3D,YAAa;gBAAAuC,QAAA,EAC5B;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAETpD,OAAA,CAACV,MAAM;gBACL+D,OAAO,EAAC,UAAU;gBAClBS,SAAS,eAAE9D,OAAA,CAACH,IAAI;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACpBW,OAAO,EAAE,IAAI,CAACvD,YAAa;gBAAAmC,QAAA,EAC5B;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAETpD,OAAA,CAACV,MAAM;gBACL+D,OAAO,EAAC,UAAU;gBAClBN,KAAK,EAAC,OAAO;gBACbe,SAAS,eAAE9D,OAAA,CAACF,SAAS;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBW,OAAO,EAAE,IAAI,CAACrD,iBAAkB;gBAAAiC,QAAA,EACjC;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAELY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI,IAAI,CAACrD,KAAK,CAACD,SAAS,iBAC7DZ,OAAA,CAACZ,GAAG;cAACqD,EAAE,EAAE;gBAAEC,EAAE,EAAE,CAAC;gBAAEG,SAAS,EAAE;cAAO,CAAE;cAAAF,QAAA,gBACpC3C,OAAA,CAACX,UAAU;gBAACgE,OAAO,EAAC,IAAI;gBAACE,YAAY;gBAAAZ,QAAA,EAAC;cAEtC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbpD,OAAA,CAACP,KAAK;gBAACgE,QAAQ,EAAC,MAAM;gBAAAd,QAAA,eACpB3C,OAAA,CAACX,UAAU;kBAACgE,OAAO,EAAC,OAAO;kBAACC,SAAS,EAAC,KAAK;kBAACb,EAAE,EAAE;oBAAEK,QAAQ,EAAE;kBAAU,CAAE;kBAAAH,QAAA,EACrE,IAAI,CAAC9B,KAAK,CAACD,SAAS,CAACK;gBAAc;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAEhB;IAEA,OAAO,IAAI,CAACjD,KAAK,CAACwC,QAAQ;EAC5B;AACF;AAEA,eAAe1C,aAAa;;AAE5B;AACA,OAAO,MAAMkE,iBAAiB,GAAGA,CAC/BhF,SAAiC,EACjCoD,QAAoB,KACjB;EACH,MAAM6B,gBAAgB,GAAIjE,KAAQ,iBAChCH,OAAA,CAACC,aAAa;IAACsC,QAAQ,EAAEA,QAAS;IAAAI,QAAA,eAChC3C,OAAA,CAACb,SAAS;MAAA,GAAKgB;IAAK;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAChB;EAEDgB,gBAAgB,CAACC,WAAW,GAAG,qBAAqBlF,SAAS,CAACkF,WAAW,IAAIlF,SAAS,CAACmF,IAAI,GAAG;EAE9F,OAAOF,gBAAgB;AACzB,CAAC;;AAED;AACA,OAAO,MAAMG,eAAe,GAAGA,CAAA,KAAM;EACnC,MAAMC,WAAW,GAAGA,CAAC7D,KAAY,EAAEC,SAAe,KAAK;IACrDmB,OAAO,CAACpB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,EAAEC,SAAS,CAAC;;IAEnE;IACA;EACF,CAAC;EAED,OAAO4D,WAAW;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}