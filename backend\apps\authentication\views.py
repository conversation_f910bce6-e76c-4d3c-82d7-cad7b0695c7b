# TrustVault - Authentication Views

import logging
import qrcode
import io
import base64
from django.contrib.auth import login, logout
from django.utils import timezone
from django.conf import settings
from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from django_otp.models import Device
from django_otp.plugins.otp_totp.models import TOTPDevice
from drf_spectacular.utils import extend_schema, OpenApiResponse
from .models import User, LoginAttempt, UserSession
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer, UserProfileSerializer,
    ChangePasswordSerializer, MFASetupSerializer, LoginAttemptSerializer,
    UserSessionSerializer
)
from apps.core.models import AuditLog

logger = logging.getLogger(__name__)


class UserRegistrationView(generics.CreateAPIView):
    """User registration endpoint."""
    
    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [permissions.AllowAny]
    
    @extend_schema(
        summary="User Registration",
        description="Register a new user account",
        responses={
            201: OpenApiResponse(description="User created successfully"),
            400: OpenApiResponse(description="Validation errors"),
        },
        tags=["Authentication"]
    )
    def post(self, request, *args, **kwargs):
        """Register new user."""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            
            # Log registration
            AuditLog.objects.create(
                user=user,
                action='CREATE',
                resource_type='User',
                resource_id=str(user.id),
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                details={'email': user.email}
            )
            
            logger.info(f"New user registered: {user.email}")
            
            return Response({
                'message': 'User registered successfully',
                'user_id': user.id
            }, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class UserLoginView(APIView):
    """User login endpoint with JWT token generation."""
    
    permission_classes = [permissions.AllowAny]
    
    @extend_schema(
        summary="User Login",
        description="Authenticate user and return JWT tokens",
        request=UserLoginSerializer,
        responses={
            200: OpenApiResponse(description="Login successful"),
            400: OpenApiResponse(description="Invalid credentials"),
            423: OpenApiResponse(description="Account locked"),
        },
        tags=["Authentication"]
    )
    def post(self, request):
        """Authenticate user and return tokens."""
        serializer = UserLoginSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            user = serializer.validated_data['user']
            
            # Check if MFA is required
            if user.is_mfa_enabled:
                # For MFA users, return a temporary token that requires MFA verification
                return Response({
                    'mfa_required': True,
                    'message': 'MFA verification required',
                    'user_id': user.id
                }, status=status.HTTP_200_OK)
            
            # Generate JWT tokens
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token
            
            # Create user session
            session = UserSession.objects.create(
                user=user,
                session_key=str(refresh),
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                expires_at=timezone.now() + timezone.timedelta(days=7)
            )
            
            # Update user login info
            user.last_login = timezone.now()
            user.last_login_ip = self._get_client_ip(request)
            user.last_login_user_agent = request.META.get('HTTP_USER_AGENT', '')
            user.save(update_fields=['last_login', 'last_login_ip', 'last_login_user_agent'])
            
            # Log successful login
            AuditLog.objects.create(
                user=user,
                action='LOGIN',
                resource_type='User',
                resource_id=str(user.id),
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                details={'session_id': str(session.id)}
            )
            
            logger.info(f"User logged in: {user.email}")
            
            return Response({
                'access_token': str(access_token),
                'refresh_token': str(refresh),
                'user': UserProfileSerializer(user).data,
                'expires_in': settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME'].total_seconds()
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class UserLogoutView(APIView):
    """User logout endpoint."""
    
    @extend_schema(
        summary="User Logout",
        description="Logout user and invalidate tokens",
        responses={200: OpenApiResponse(description="Logout successful")},
        tags=["Authentication"]
    )
    def post(self, request):
        """Logout user."""
        try:
            # Get refresh token from request
            refresh_token = request.data.get('refresh_token')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()
            
            # Deactivate user session
            sessions = UserSession.objects.filter(
                user=request.user,
                is_active=True
            )
            sessions.update(is_active=False)
            
            # Log logout
            AuditLog.objects.create(
                user=request.user,
                action='LOGOUT',
                resource_type='User',
                resource_id=str(request.user.id),
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
            )
            
            logger.info(f"User logged out: {request.user.email}")
            
            return Response({
                'message': 'Logout successful'
            }, status=status.HTTP_200_OK)
        
        except Exception as e:
            logger.error(f"Logout error: {str(e)}")
            return Response({
                'message': 'Logout completed'
            }, status=status.HTTP_200_OK)
    
    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class UserProfileView(generics.RetrieveUpdateAPIView):
    """User profile endpoint."""
    
    serializer_class = UserProfileSerializer
    
    @extend_schema(
        summary="Get User Profile",
        description="Get current user profile information",
        responses={200: UserProfileSerializer},
        tags=["Authentication"]
    )
    def get(self, request, *args, **kwargs):
        """Get user profile."""
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="Update User Profile",
        description="Update current user profile information",
        request=UserProfileSerializer,
        responses={200: UserProfileSerializer},
        tags=["Authentication"]
    )
    def put(self, request, *args, **kwargs):
        """Update user profile."""
        return super().put(request, *args, **kwargs)
    
    def get_object(self):
        """Get current user."""
        return self.request.user
    
    def perform_update(self, serializer):
        """Log profile update."""
        user = serializer.save()
        
        AuditLog.objects.create(
            user=user,
            action='UPDATE',
            resource_type='UserProfile',
            resource_id=str(user.id),
            ip_address=self._get_client_ip(self.request),
            user_agent=self.request.META.get('HTTP_USER_AGENT', ''),
            details={'updated_fields': list(serializer.validated_data.keys())}
        )
        
        logger.info(f"User profile updated: {user.email}")
    
    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class ChangePasswordView(APIView):
    """Change password endpoint."""
    
    @extend_schema(
        summary="Change Password",
        description="Change user password",
        request=ChangePasswordSerializer,
        responses={200: OpenApiResponse(description="Password changed successfully")},
        tags=["Authentication"]
    )
    def post(self, request):
        """Change user password."""
        serializer = ChangePasswordSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            serializer.save()
            
            # Log password change
            AuditLog.objects.create(
                user=request.user,
                action='UPDATE',
                resource_type='UserPassword',
                resource_id=str(request.user.id),
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                severity='HIGH'
            )
            
            logger.info(f"Password changed for user: {request.user.email}")
            
            return Response({
                'message': 'Password changed successfully'
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class MFASetupView(APIView):
    """MFA setup endpoint."""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Setup MFA",
        description="Setup Multi-Factor Authentication for user",
        responses={
            200: OpenApiResponse(description="MFA setup initiated"),
            400: OpenApiResponse(description="MFA already enabled"),
        },
        tags=["MFA"]
    )
    def post(self, request):
        """Setup MFA for user."""
        user = request.user

        if user.is_mfa_enabled:
            return Response({
                'error': 'MFA is already enabled'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create or get TOTP device
        device, created = TOTPDevice.objects.get_or_create(
            user=user,
            name='default',
            defaults={'confirmed': False}
        )

        if not created and device.confirmed:
            return Response({
                'error': 'MFA is already enabled'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Generate QR code
        qr_code_url = device.config_url

        # Log MFA setup attempt
        AuditLog.objects.create(
            user=user,
            action='MFA_SETUP_INITIATED',
            resource_type='User',
            resource_id=str(user.id),
            ip_address=self._get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            details={'device_name': device.name}
        )

        return Response({
            'qr_code_url': qr_code_url,
            'secret_key': device.key,
            'message': 'Scan the QR code with your authenticator app'
        }, status=status.HTTP_200_OK)

    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class MFAVerifyView(APIView):
    """MFA verification endpoint."""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Verify MFA",
        description="Verify MFA token and complete setup",
        request=MFASetupSerializer,
        responses={
            200: OpenApiResponse(description="MFA verified successfully"),
            400: OpenApiResponse(description="Invalid token"),
        },
        tags=["MFA"]
    )
    def post(self, request):
        """Verify MFA token."""
        serializer = MFASetupSerializer(data=request.data, context={'request': request})

        if serializer.is_valid():
            serializer.save()

            # Log MFA setup completion
            AuditLog.objects.create(
                user=request.user,
                action='MFA_ENABLED',
                resource_type='User',
                resource_id=str(request.user.id),
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                severity='HIGH'
            )

            logger.info(f"MFA enabled for user: {request.user.email}")

            return Response({
                'message': 'MFA enabled successfully'
            }, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class MFADisableView(APIView):
    """MFA disable endpoint."""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Disable MFA",
        description="Disable Multi-Factor Authentication for user",
        responses={
            200: OpenApiResponse(description="MFA disabled successfully"),
            400: OpenApiResponse(description="MFA not enabled"),
        },
        tags=["MFA"]
    )
    def post(self, request):
        """Disable MFA for user."""
        user = request.user

        if not user.is_mfa_enabled:
            return Response({
                'error': 'MFA is not enabled'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Remove all TOTP devices
        user.totpdevice_set.all().delete()

        # Disable MFA
        user.is_mfa_enabled = False
        user.save(update_fields=['is_mfa_enabled'])

        # Log MFA disable
        AuditLog.objects.create(
            user=user,
            action='MFA_DISABLED',
            resource_type='User',
            resource_id=str(user.id),
            ip_address=self._get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            severity='HIGH'
        )

        logger.warning(f"MFA disabled for user: {user.email}")

        return Response({
            'message': 'MFA disabled successfully'
        }, status=status.HTTP_200_OK)

    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class MFAQRCodeView(APIView):
    """MFA QR Code generation endpoint."""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Get MFA QR Code",
        description="Get QR code for MFA setup",
        responses={
            200: OpenApiResponse(description="QR code generated"),
            400: OpenApiResponse(description="No MFA setup in progress"),
        },
        tags=["MFA"]
    )
    def get(self, request):
        """Get QR code for MFA setup."""
        user = request.user

        # Get unconfirmed TOTP device
        device = user.totpdevice_set.filter(confirmed=False).first()

        if not device:
            return Response({
                'error': 'No MFA setup in progress'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Generate QR code image
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(device.config_url)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")

        # Convert to base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()

        return Response({
            'qr_code': f'data:image/png;base64,{img_str}',
            'secret_key': device.key,
            'config_url': device.config_url
        }, status=status.HTTP_200_OK)


class UserSessionsView(generics.ListAPIView):
    """List user sessions."""

    serializer_class = UserSessionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Get user's active sessions."""
        return UserSession.objects.filter(
            user=self.request.user,
            is_active=True
        ).order_by('-last_activity')

    @extend_schema(
        summary="List User Sessions",
        description="Get list of user's active sessions",
        responses={200: UserSessionSerializer(many=True)},
        tags=["Sessions"]
    )
    def get(self, request, *args, **kwargs):
        """List user's active sessions."""
        return super().get(request, *args, **kwargs)


class RevokeSessionView(APIView):
    """Revoke user session."""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Revoke Session",
        description="Revoke a specific user session",
        responses={
            200: OpenApiResponse(description="Session revoked successfully"),
            404: OpenApiResponse(description="Session not found"),
        },
        tags=["Sessions"]
    )
    def post(self, request, session_id):
        """Revoke a specific session."""
        try:
            session = UserSession.objects.get(
                id=session_id,
                user=request.user,
                is_active=True
            )

            session.is_active = False
            session.save(update_fields=['is_active'])

            # Log session revocation
            AuditLog.objects.create(
                user=request.user,
                action='SESSION_REVOKED',
                resource_type='UserSession',
                resource_id=str(session.id),
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                details={'revoked_session_ip': session.ip_address}
            )

            return Response({
                'message': 'Session revoked successfully'
            }, status=status.HTTP_200_OK)

        except UserSession.DoesNotExist:
            return Response({
                'error': 'Session not found'
            }, status=status.HTTP_404_NOT_FOUND)

    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
