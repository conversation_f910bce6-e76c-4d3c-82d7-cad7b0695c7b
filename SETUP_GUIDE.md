# 🚀 TrustVault Setup Guide

This comprehensive guide will help you set up and run TrustVault on your local machine or deploy it to production.

## 📋 Prerequisites

### Required Software
- **Docker Desktop** (Windows/Mac) or **Docker + Docker Compose** (Linux)
- **Git** for version control
- **Node.js 18+** (for local frontend development)
- **Python 3.11+** (for local backend development)

### System Requirements
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: At least 2GB free space
- **Ports**: 3000, 8000, 5432, 6379 should be available

## 🎯 Quick Start (Recommended)

### 1. Clone the Repository
```bash
git clone <your-repository-url>
cd trustvault
```

### 2. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings (optional for development)
# The default values work for local development
```

### 3. Start the Application

#### Windows Users
```cmd
# Start development environment
start.bat development

# Or start production environment
start.bat production
```

#### Linux/Mac Users
```bash
# Make script executable
chmod +x start.sh

# Start development environment
./start.sh development

# Or start production environment
./start.sh production
```

### 4. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/api/docs/
- **Admin Panel**: http://localhost:8000/admin/

### 5. Default Login Credentials
```
Email: <EMAIL>
Password: admin123
```

**⚠️ Important**: Change these credentials immediately!

## 🛠️ Manual Setup (Advanced)

### Backend Setup

1. **Navigate to backend directory**:
   ```bash
   cd backend
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   
   # Activate virtual environment
   # Windows:
   venv\Scripts\activate
   
   # Linux/Mac:
   source venv/bin/activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Setup database**:
   ```bash
   # Run migrations
   python manage.py migrate
   
   # Create superuser
   python manage.py createsuperuser
   
   # Load initial data (optional)
   python manage.py loaddata fixtures/initial_data.json
   ```

5. **Run development server**:
   ```bash
   python manage.py runserver
   ```

### Frontend Setup

1. **Navigate to frontend directory**:
   ```bash
   cd frontend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start development server**:
   ```bash
   npm start
   ```

## 🐳 Docker Setup (Recommended for Production)

### Development Environment
```bash
# Start all services
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop services
docker-compose -f docker-compose.dev.yml down
```

### Production Environment
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## ⚙️ Configuration

### Environment Variables

Edit the `.env` file to customize your installation:

```env
# Database Configuration
POSTGRES_DB=trustvault
POSTGRES_USER=trustvault
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Django Configuration
DJANGO_SECRET_KEY=your_very_long_secret_key_here
DJANGO_DEBUG=False
DJANGO_ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# Security Configuration
ENCRYPTION_KEY=your_32_character_encryption_key
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ACCESS_TOKEN_LIFETIME=15
JWT_REFRESH_TOKEN_LIFETIME=7

# Redis Configuration
REDIS_URL=redis://redis:6379/0

# Email Configuration (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8000/api/v1
REACT_APP_ENVIRONMENT=development
```

### Security Settings

For production deployment, ensure you:

1. **Change default passwords**
2. **Use strong secret keys**
3. **Enable HTTPS**
4. **Configure proper CORS settings**
5. **Set up proper firewall rules**

## 🧪 Testing

### Run All Tests
```bash
# Using the test runner script
python run_tests.py

# Or manually run backend tests
cd backend
python manage.py test

# Or manually run frontend tests
cd frontend
npm test
```

### Test Coverage
```bash
# Backend coverage
cd backend
coverage run --source='.' manage.py test
coverage report
coverage html

# Frontend coverage
cd frontend
npm test -- --coverage
```

## 🚀 Deployment

### Production Checklist

- [ ] Update environment variables for production
- [ ] Change default admin credentials
- [ ] Configure HTTPS/SSL certificates
- [ ] Set up proper database backups
- [ ] Configure monitoring and logging
- [ ] Set up firewall rules
- [ ] Configure domain and DNS
- [ ] Test all functionality

### Docker Production Deployment

1. **Prepare production environment**:
   ```bash
   cp .env.example .env.production
   # Edit .env.production with production values
   ```

2. **Deploy with Docker**:
   ```bash
   docker-compose --env-file .env.production up -d
   ```

3. **Setup SSL/TLS** (recommended):
   ```bash
   # Using Let's Encrypt with Certbot
   sudo certbot --nginx -d your-domain.com
   ```

### Cloud Deployment Options

#### AWS Deployment
- Use ECS or EKS for container orchestration
- RDS for PostgreSQL database
- ElastiCache for Redis
- ALB for load balancing
- Route 53 for DNS

#### Google Cloud Platform
- Use GKE for Kubernetes deployment
- Cloud SQL for PostgreSQL
- Memorystore for Redis
- Cloud Load Balancing
- Cloud DNS

#### DigitalOcean
- Use App Platform for easy deployment
- Managed PostgreSQL database
- Managed Redis cluster
- Load Balancer
- DNS management

## 🔧 Troubleshooting

### Common Issues

#### Docker Issues
```bash
# Problem: Containers won't start
# Solution: Check if ports are available
netstat -tulpn | grep :3000
netstat -tulpn | grep :8000

# Problem: Database connection errors
# Solution: Wait for PostgreSQL to initialize
docker-compose logs postgres
```

#### Backend Issues
```bash
# Problem: Migration errors
# Solution: Reset migrations (development only)
python manage.py migrate --fake-initial

# Problem: Static files not loading
# Solution: Collect static files
python manage.py collectstatic --noinput
```

#### Frontend Issues
```bash
# Problem: Build errors
# Solution: Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Problem: API connection errors
# Solution: Check backend is running
curl http://localhost:8000/health/
```

### Getting Help

1. **Check the logs**:
   ```bash
   # Docker logs
   docker-compose logs -f [service-name]
   
   # Application logs
   tail -f backend/logs/django.log
   ```

2. **Verify service health**:
   ```bash
   # Backend health
   curl http://localhost:8000/health/
   
   # Frontend health
   curl http://localhost:3000/health
   ```

3. **Database connection**:
   ```bash
   # Connect to PostgreSQL
   docker-compose exec postgres psql -U trustvault -d trustvault
   ```

## 📚 Additional Resources

- **API Documentation**: http://localhost:8000/api/docs/
- **Admin Interface**: http://localhost:8000/admin/
- **Project Repository**: [Your GitHub/GitLab URL]
- **Issue Tracker**: [Your Issues URL]

## 🆘 Support

If you encounter any issues:

1. Check this setup guide
2. Review the troubleshooting section
3. Check existing issues in the repository
4. Create a new issue with detailed information

---

**Happy coding with TrustVault! 🔐**
