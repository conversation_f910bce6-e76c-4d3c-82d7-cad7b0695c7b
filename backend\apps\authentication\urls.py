# TrustVault - Authentication URLs

from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

app_name = 'authentication'

urlpatterns = [
    # Authentication
    path('register/', views.UserRegistrationView.as_view(), name='register'),
    path('login/', views.UserLoginView.as_view(), name='login'),
    path('logout/', views.UserLogoutView.as_view(), name='logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token-refresh'),

    # Profile
    path('profile/', views.UserProfileView.as_view(), name='profile'),
    path('change-password/', views.ChangePasswordView.as_view(), name='change-password'),

    # MFA
    path('mfa/setup/', views.MFASetupView.as_view(), name='mfa-setup'),
    path('mfa/verify/', views.MFAVerifyView.as_view(), name='mfa-verify'),
    path('mfa/disable/', views.MFADisableView.as_view(), name='mfa-disable'),
    path('mfa/qr-code/', views.MFAQRCodeView.as_view(), name='mfa-qr-code'),

    # Sessions
    path('sessions/', views.UserSessionsView.as_view(), name='sessions'),
    path('sessions/<uuid:session_id>/revoke/', views.RevokeSessionView.as_view(), name='revoke-session'),
]
