{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\pages\\\\Portfolio\\\\AddHoldingPage.tsx\",\n  _s = $RefreshSig$();\n// TrustVault - Add Holding Page\n\nimport React, { useState, createElement as _createElement } from 'react';\nimport { Box, Typography, Paper, TextField, Button, Autocomplete, Alert, CircularProgress, Grid, InputAdornment } from '@mui/material';\nimport { ArrowBack, Add } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useMutation, useQuery, useQueryClient } from 'react-query';\nimport { toast } from 'react-hot-toast';\n\n// Services\nimport apiService from '../../services/api';\n\n// Types\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AddHoldingPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    id: portfolioId\n  } = useParams();\n  const queryClient = useQueryClient();\n  const [formData, setFormData] = useState({\n    asset_id: '',\n    quantity: 0,\n    purchase_price: 0,\n    purchase_date: new Date().toISOString().split('T')[0],\n    notes: ''\n  });\n  const [selectedAsset, setSelectedAsset] = useState(null);\n  const [errors, setErrors] = useState({});\n\n  // Fetch available assets\n  const {\n    data: assets,\n    isLoading: assetsLoading,\n    error: assetsError\n  } = useQuery('assets', () => apiService.getAssets(), {\n    retry: 3,\n    staleTime: 5 * 60 * 1000 // 5 minutes\n  });\n\n  // Ensure assets is always an array\n  const assetOptions = React.useMemo(() => {\n    console.log('Assets data:', assets); // Debug log\n\n    if (!assets) {\n      console.log('Assets is null/undefined');\n      return [];\n    }\n    if (!Array.isArray(assets)) {\n      console.log('Assets is not an array:', typeof assets, assets);\n      return [];\n    }\n    console.log('Assets array length:', assets.length);\n    return assets;\n  }, [assets]);\n\n  // Fetch portfolio details\n  const {\n    data: portfolio\n  } = useQuery(['portfolio', portfolioId], () => apiService.getPortfolio(portfolioId), {\n    enabled: !!portfolioId\n  });\n  const addHoldingMutation = useMutation(data => {\n    if (!portfolioId) throw new Error('Portfolio ID is required');\n    return apiService.createHolding(portfolioId, data);\n  }, {\n    onSuccess: () => {\n      toast.success('Holding added successfully!');\n      queryClient.invalidateQueries(['portfolio', portfolioId]);\n      queryClient.invalidateQueries(['holdings', portfolioId]);\n      navigate(`/portfolios/${portfolioId}`);\n    },\n    onError: error => {\n      var _error$response;\n      console.error('Add holding error:', error);\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && _error$response.data) {\n        setErrors(error.response.data);\n      } else {\n        toast.error('Failed to add holding. Please try again.');\n      }\n    }\n  });\n  const handleInputChange = field => event => {\n    const value = event.target.type === 'number' ? parseFloat(event.target.value) || 0 : event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear error when user starts typing\n    if (field in errors) {\n      setErrors(prev => {\n        const newErrors = {\n          ...prev\n        };\n        delete newErrors[field];\n        return newErrors;\n      });\n    }\n  };\n  const handleAssetChange = (event, newValue) => {\n    console.log('Asset change:', newValue); // Debug log\n\n    setSelectedAsset(newValue);\n    setFormData(prev => ({\n      ...prev,\n      asset_id: (newValue === null || newValue === void 0 ? void 0 : newValue.id) || ''\n    }));\n\n    // Clear asset_id error when user selects an asset\n    if (errors.asset_id) {\n      setErrors(prev => {\n        const newErrors = {\n          ...prev\n        };\n        delete newErrors.asset_id;\n        return newErrors;\n      });\n    }\n  };\n  const handleSubmit = event => {\n    event.preventDefault();\n\n    // Basic validation\n    const newErrors = {};\n    if (!formData.asset_id) {\n      newErrors.asset_id = 'Please select an asset';\n    }\n    if (formData.quantity <= 0) {\n      newErrors.quantity = 'Quantity must be greater than 0';\n    }\n    if (formData.purchase_price <= 0) {\n      newErrors.purchase_price = 'Purchase price must be greater than 0';\n    }\n    if (!formData.purchase_date) {\n      newErrors.purchase_date = 'Purchase date is required';\n    }\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    addHoldingMutation.mutate(formData);\n  };\n  const calculateTotalValue = () => {\n    return formData.quantity * formData.purchase_price;\n  };\n  if (!portfolioId) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: 800,\n        mx: 'auto',\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"error\",\n        children: \"Portfolio ID is required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Add Holding - TrustVault\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Add a new holding to your portfolio\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: 800,\n        mx: 'auto',\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 24\n          }, this),\n          onClick: () => navigate(`/portfolios/${portfolioId}`),\n          sx: {\n            mr: 2\n          },\n          children: \"Back to Portfolio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            component: \"h1\",\n            children: \"Add New Holding\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), portfolio && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"to \", portfolio.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: assetsError ? /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"error\",\n                sx: {\n                  mb: 2\n                },\n                children: [\"Failed to load assets. Please check if the backend server is running and refresh the page.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: [\"Error: \", (assetsError === null || assetsError === void 0 ? void 0 : assetsError.message) || 'Unknown error']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this) : !assetsLoading && assetOptions.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"warning\",\n                sx: {\n                  mb: 2\n                },\n                children: \"No assets available. Please contact your administrator to add assets to the system.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Autocomplete, {\n                options: assetOptions,\n                getOptionLabel: option => {\n                  if (!option || typeof option !== 'object') return '';\n                  return `${option.symbol || 'N/A'} - ${option.name || 'Unknown Asset'}`;\n                },\n                value: selectedAsset,\n                onChange: handleAssetChange,\n                loading: assetsLoading,\n                disabled: assetsLoading || !!assetsError || assetOptions.length === 0,\n                isOptionEqualToValue: (option, value) => {\n                  if (!option || !value) return false;\n                  return option.id === value.id;\n                },\n                noOptionsText: assetsLoading ? \"Loading...\" : \"No assets found\",\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  label: \"Select Asset\",\n                  error: !!errors.asset_id,\n                  helperText: errors.asset_id || (assetsLoading ? 'Loading assets...' : assetOptions.length === 0 ? 'No assets available' : `${assetOptions.length} assets available`),\n                  required: true,\n                  InputProps: {\n                    ...params.InputProps,\n                    endAdornment: /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [assetsLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                        color: \"inherit\",\n                        size: 20\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 273,\n                        columnNumber: 48\n                      }, this) : null, params.InputProps.endAdornment]\n                    }, void 0, true)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 23\n                }, this),\n                renderOption: (props, option) => /*#__PURE__*/_createElement(Box, {\n                  component: \"li\",\n                  ...props,\n                  key: option.id || Math.random(),\n                  __self: this,\n                  __source: {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 23\n                  }\n                }, /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body1\",\n                    children: [option.symbol || 'N/A', \" - \", option.name || 'Unknown Asset']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [option.asset_type || 'Unknown', \" \\u2022 \", option.sector || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Quantity\",\n                type: \"number\",\n                value: formData.quantity || '',\n                onChange: handleInputChange('quantity'),\n                error: !!errors.quantity,\n                helperText: errors.quantity,\n                required: true,\n                fullWidth: true,\n                inputProps: {\n                  min: 0,\n                  step: 0.01\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Purchase Price\",\n                type: \"number\",\n                value: formData.purchase_price || '',\n                onChange: handleInputChange('purchase_price'),\n                error: !!errors.purchase_price,\n                helperText: errors.purchase_price,\n                required: true,\n                fullWidth: true,\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: \"$\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 37\n                  }, this)\n                },\n                inputProps: {\n                  min: 0,\n                  step: 0.01\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Purchase Date\",\n                type: \"date\",\n                value: formData.purchase_date,\n                onChange: handleInputChange('purchase_date'),\n                error: !!errors.purchase_date,\n                helperText: errors.purchase_date,\n                required: true,\n                fullWidth: true,\n                InputLabelProps: {\n                  shrink: true\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Total Value\",\n                value: `$${calculateTotalValue().toFixed(2)}`,\n                fullWidth: true,\n                disabled: true,\n                InputProps: {\n                  readOnly: true\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Notes (Optional)\",\n                value: formData.notes,\n                onChange: handleInputChange('notes'),\n                multiline: true,\n                rows: 3,\n                fullWidth: true,\n                placeholder: \"Add any notes about this holding...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this), Object.keys(errors).length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"error\",\n                children: \"Please fix the errors above and try again.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                justifyContent: \"flex-end\",\n                gap: 2,\n                mt: 2,\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  onClick: () => navigate(`/portfolios/${portfolioId}`),\n                  disabled: addHoldingMutation.isLoading,\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  variant: \"contained\",\n                  startIcon: addHoldingMutation.isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 63\n                  }, this) : /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 96\n                  }, this),\n                  disabled: addHoldingMutation.isLoading,\n                  children: addHoldingMutation.isLoading ? 'Adding...' : 'Add Holding'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mt: 3,\n          bgcolor: 'background.default'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"About Holdings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Holdings represent your investments in specific assets. Once added, you can:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          component: \"ul\",\n          sx: {\n            mt: 1,\n            pl: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Track performance and current value\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Record additional transactions (buy/sell)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"View detailed analytics and charts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Monitor portfolio allocation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AddHoldingPage, \"cCPfDBcs24XM5ku4ae8aEN1DUXk=\", false, function () {\n  return [useNavigate, useParams, useQueryClient, useQuery, useQuery, useMutation];\n});\n_c = AddHoldingPage;\nexport default AddHoldingPage;\nvar _c;\n$RefreshReg$(_c, \"AddHoldingPage\");", "map": {"version": 3, "names": ["React", "useState", "createElement", "_createElement", "Box", "Typography", "Paper", "TextField", "<PERSON><PERSON>", "Autocomplete", "<PERSON><PERSON>", "CircularProgress", "Grid", "InputAdornment", "ArrowBack", "Add", "<PERSON><PERSON><PERSON>", "useNavigate", "useParams", "useMutation", "useQuery", "useQueryClient", "toast", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddHoldingPage", "_s", "navigate", "id", "portfolioId", "queryClient", "formData", "setFormData", "asset_id", "quantity", "purchase_price", "purchase_date", "Date", "toISOString", "split", "notes", "selectedAsset", "setSelectedAsset", "errors", "setErrors", "data", "assets", "isLoading", "assetsLoading", "error", "assetsError", "getAssets", "retry", "staleTime", "assetOptions", "useMemo", "console", "log", "Array", "isArray", "length", "portfolio", "getPortfolio", "enabled", "addHoldingMutation", "Error", "createHolding", "onSuccess", "success", "invalidateQueries", "onError", "_error$response", "response", "handleInputChange", "field", "event", "value", "target", "type", "parseFloat", "prev", "newErrors", "handleAssetChange", "newValue", "handleSubmit", "preventDefault", "Object", "keys", "mutate", "calculateTotalValue", "sx", "max<PERSON><PERSON><PERSON>", "mx", "p", "children", "variant", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "display", "alignItems", "mb", "startIcon", "onClick", "mr", "component", "onSubmit", "container", "spacing", "item", "xs", "severity", "message", "options", "getOptionLabel", "option", "symbol", "onChange", "loading", "disabled", "isOptionEqualToValue", "noOptionsText", "renderInput", "params", "label", "helperText", "required", "InputProps", "endAdornment", "size", "renderOption", "props", "key", "Math", "random", "__self", "__source", "asset_type", "sector", "sm", "fullWidth", "inputProps", "min", "step", "startAdornment", "position", "InputLabelProps", "shrink", "toFixed", "readOnly", "multiline", "rows", "placeholder", "justifyContent", "gap", "mt", "bgcolor", "gutterBottom", "pl", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/pages/Portfolio/AddHoldingPage.tsx"], "sourcesContent": ["// TrustVault - Add Holding Page\n\nimport React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  TextField,\n  Button,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Autocomplete,\n  Alert,\n  CircularProgress,\n  Grid,\n  InputAdornment,\n} from '@mui/material';\nimport { ArrowBack, Add } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useMutation, useQuery, useQueryClient } from 'react-query';\nimport { toast } from 'react-hot-toast';\n\n// Services\nimport apiService from '../../services/api';\n\n// Types\ninterface AddHoldingForm {\n  asset_id: string;\n  quantity: number;\n  purchase_price: number;\n  purchase_date: string;\n  notes?: string;\n}\n\nconst AddHoldingPage: React.FC = () => {\n  const navigate = useNavigate();\n  const { id: portfolioId } = useParams<{ id: string }>();\n  const queryClient = useQueryClient();\n\n  const [formData, setFormData] = useState<AddHoldingForm>({\n    asset_id: '',\n    quantity: 0,\n    purchase_price: 0,\n    purchase_date: new Date().toISOString().split('T')[0],\n    notes: '',\n  });\n\n  const [selectedAsset, setSelectedAsset] = useState<any>(null);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  // Fetch available assets\n  const { data: assets, isLoading: assetsLoading, error: assetsError } = useQuery(\n    'assets',\n    () => apiService.getAssets(),\n    {\n      retry: 3,\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    }\n  );\n\n  // Ensure assets is always an array\n  const assetOptions = React.useMemo(() => {\n    console.log('Assets data:', assets); // Debug log\n\n    if (!assets) {\n      console.log('Assets is null/undefined');\n      return [];\n    }\n\n    if (!Array.isArray(assets)) {\n      console.log('Assets is not an array:', typeof assets, assets);\n      return [];\n    }\n\n    console.log('Assets array length:', assets.length);\n    return assets;\n  }, [assets]);\n\n  // Fetch portfolio details\n  const { data: portfolio } = useQuery(\n    ['portfolio', portfolioId],\n    () => apiService.getPortfolio(portfolioId!),\n    {\n      enabled: !!portfolioId,\n    }\n  );\n\n  const addHoldingMutation = useMutation(\n    (data: AddHoldingForm) => {\n      if (!portfolioId) throw new Error('Portfolio ID is required');\n      return apiService.createHolding(portfolioId, data);\n    },\n    {\n      onSuccess: () => {\n        toast.success('Holding added successfully!');\n        queryClient.invalidateQueries(['portfolio', portfolioId]);\n        queryClient.invalidateQueries(['holdings', portfolioId]);\n        navigate(`/portfolios/${portfolioId}`);\n      },\n      onError: (error: any) => {\n        console.error('Add holding error:', error);\n        if (error.response?.data) {\n          setErrors(error.response.data);\n        } else {\n          toast.error('Failed to add holding. Please try again.');\n        }\n      },\n    }\n  );\n\n  const handleInputChange = (field: keyof AddHoldingForm) => (\n    event: React.ChangeEvent<HTMLInputElement>\n  ) => {\n    const value = event.target.type === 'number' ? parseFloat(event.target.value) || 0 : event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value,\n    }));\n    \n    // Clear error when user starts typing\n    if (field in errors) {\n      setErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors[field];\n        return newErrors;\n      });\n    }\n  };\n\n  const handleAssetChange = (event: any, newValue: any) => {\n    console.log('Asset change:', newValue); // Debug log\n\n    setSelectedAsset(newValue);\n    setFormData(prev => ({\n      ...prev,\n      asset_id: newValue?.id || '',\n    }));\n\n    // Clear asset_id error when user selects an asset\n    if (errors.asset_id) {\n      setErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors.asset_id;\n        return newErrors;\n      });\n    }\n  };\n\n  const handleSubmit = (event: React.FormEvent) => {\n    event.preventDefault();\n    \n    // Basic validation\n    const newErrors: Record<string, string> = {};\n    \n    if (!formData.asset_id) {\n      newErrors.asset_id = 'Please select an asset';\n    }\n    \n    if (formData.quantity <= 0) {\n      newErrors.quantity = 'Quantity must be greater than 0';\n    }\n    \n    if (formData.purchase_price <= 0) {\n      newErrors.purchase_price = 'Purchase price must be greater than 0';\n    }\n    \n    if (!formData.purchase_date) {\n      newErrors.purchase_date = 'Purchase date is required';\n    }\n    \n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    \n    addHoldingMutation.mutate(formData);\n  };\n\n  const calculateTotalValue = () => {\n    return formData.quantity * formData.purchase_price;\n  };\n\n  if (!portfolioId) {\n    return (\n      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>\n        <Typography variant=\"h6\" color=\"error\">\n          Portfolio ID is required\n        </Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <>\n      <Helmet>\n        <title>Add Holding - TrustVault</title>\n        <meta name=\"description\" content=\"Add a new holding to your portfolio\" />\n      </Helmet>\n\n      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>\n        {/* Header */}\n        <Box display=\"flex\" alignItems=\"center\" mb={3}>\n          <Button\n            startIcon={<ArrowBack />}\n            onClick={() => navigate(`/portfolios/${portfolioId}`)}\n            sx={{ mr: 2 }}\n          >\n            Back to Portfolio\n          </Button>\n          <Box>\n            <Typography variant=\"h4\" component=\"h1\">\n              Add New Holding\n            </Typography>\n            {portfolio && (\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                to {portfolio.name}\n              </Typography>\n            )}\n          </Box>\n        </Box>\n\n        {/* Form */}\n        <Paper sx={{ p: 4 }}>\n          <form onSubmit={handleSubmit}>\n            <Grid container spacing={3}>\n              {/* Asset Selection */}\n              <Grid item xs={12}>\n                {assetsError ? (\n                  <Alert severity=\"error\" sx={{ mb: 2 }}>\n                    Failed to load assets. Please check if the backend server is running and refresh the page.\n                    <br />\n                    <small>Error: {assetsError?.message || 'Unknown error'}</small>\n                  </Alert>\n                ) : !assetsLoading && assetOptions.length === 0 ? (\n                  <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                    No assets available. Please contact your administrator to add assets to the system.\n                  </Alert>\n                ) : (\n                  <Autocomplete\n                    options={assetOptions}\n                    getOptionLabel={(option) => {\n                      if (!option || typeof option !== 'object') return '';\n                      return `${option.symbol || 'N/A'} - ${option.name || 'Unknown Asset'}`;\n                    }}\n                    value={selectedAsset}\n                    onChange={handleAssetChange}\n                    loading={assetsLoading}\n                    disabled={assetsLoading || !!assetsError || assetOptions.length === 0}\n                    isOptionEqualToValue={(option, value) => {\n                      if (!option || !value) return false;\n                      return option.id === value.id;\n                    }}\n                    noOptionsText={assetsLoading ? \"Loading...\" : \"No assets found\"}\n                    renderInput={(params) => (\n                      <TextField\n                        {...params}\n                        label=\"Select Asset\"\n                        error={!!errors.asset_id}\n                        helperText={\n                          errors.asset_id ||\n                          (assetsLoading ? 'Loading assets...' :\n                           assetOptions.length === 0 ? 'No assets available' :\n                           `${assetOptions.length} assets available`)\n                        }\n                        required\n                        InputProps={{\n                          ...params.InputProps,\n                          endAdornment: (\n                            <>\n                              {assetsLoading ? <CircularProgress color=\"inherit\" size={20} /> : null}\n                              {params.InputProps.endAdornment}\n                            </>\n                          ),\n                        }}\n                      />\n                    )}\n                    renderOption={(props, option) => (\n                      <Box component=\"li\" {...props} key={option.id || Math.random()}>\n                        <Box>\n                          <Typography variant=\"body1\">\n                            {option.symbol || 'N/A'} - {option.name || 'Unknown Asset'}\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            {option.asset_type || 'Unknown'} • {option.sector || 'N/A'}\n                          </Typography>\n                        </Box>\n                      </Box>\n                    )}\n                  />\n                )}\n              </Grid>\n\n              {/* Quantity */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  label=\"Quantity\"\n                  type=\"number\"\n                  value={formData.quantity || ''}\n                  onChange={handleInputChange('quantity')}\n                  error={!!errors.quantity}\n                  helperText={errors.quantity}\n                  required\n                  fullWidth\n                  inputProps={{ min: 0, step: 0.01 }}\n                />\n              </Grid>\n\n              {/* Purchase Price */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  label=\"Purchase Price\"\n                  type=\"number\"\n                  value={formData.purchase_price || ''}\n                  onChange={handleInputChange('purchase_price')}\n                  error={!!errors.purchase_price}\n                  helperText={errors.purchase_price}\n                  required\n                  fullWidth\n                  InputProps={{\n                    startAdornment: <InputAdornment position=\"start\">$</InputAdornment>,\n                  }}\n                  inputProps={{ min: 0, step: 0.01 }}\n                />\n              </Grid>\n\n              {/* Purchase Date */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  label=\"Purchase Date\"\n                  type=\"date\"\n                  value={formData.purchase_date}\n                  onChange={handleInputChange('purchase_date')}\n                  error={!!errors.purchase_date}\n                  helperText={errors.purchase_date}\n                  required\n                  fullWidth\n                  InputLabelProps={{\n                    shrink: true,\n                  }}\n                />\n              </Grid>\n\n              {/* Total Value Display */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  label=\"Total Value\"\n                  value={`$${calculateTotalValue().toFixed(2)}`}\n                  fullWidth\n                  disabled\n                  InputProps={{\n                    readOnly: true,\n                  }}\n                />\n              </Grid>\n\n              {/* Notes */}\n              <Grid item xs={12}>\n                <TextField\n                  label=\"Notes (Optional)\"\n                  value={formData.notes}\n                  onChange={handleInputChange('notes')}\n                  multiline\n                  rows={3}\n                  fullWidth\n                  placeholder=\"Add any notes about this holding...\"\n                />\n              </Grid>\n\n              {/* Error Display */}\n              {Object.keys(errors).length > 0 && (\n                <Grid item xs={12}>\n                  <Alert severity=\"error\">\n                    Please fix the errors above and try again.\n                  </Alert>\n                </Grid>\n              )}\n\n              {/* Submit Buttons */}\n              <Grid item xs={12}>\n                <Box display=\"flex\" justifyContent=\"flex-end\" gap={2} mt={2}>\n                  <Button\n                    variant=\"outlined\"\n                    onClick={() => navigate(`/portfolios/${portfolioId}`)}\n                    disabled={addHoldingMutation.isLoading}\n                  >\n                    Cancel\n                  </Button>\n                  <Button\n                    type=\"submit\"\n                    variant=\"contained\"\n                    startIcon={addHoldingMutation.isLoading ? <CircularProgress size={20} /> : <Add />}\n                    disabled={addHoldingMutation.isLoading}\n                  >\n                    {addHoldingMutation.isLoading ? 'Adding...' : 'Add Holding'}\n                  </Button>\n                </Box>\n              </Grid>\n            </Grid>\n          </form>\n        </Paper>\n\n        {/* Info Box */}\n        <Paper sx={{ p: 3, mt: 3, bgcolor: 'background.default' }}>\n          <Typography variant=\"h6\" gutterBottom>\n            About Holdings\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Holdings represent your investments in specific assets. Once added, you can:\n          </Typography>\n          <Box component=\"ul\" sx={{ mt: 1, pl: 2 }}>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Track performance and current value\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Record additional transactions (buy/sell)\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              View detailed analytics and charts\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Monitor portfolio allocation\n            </Typography>\n          </Box>\n        </Paper>\n      </Box>\n    </>\n  );\n};\n\nexport default AddHoldingPage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAAC,aAAA,IAAAC,cAAA,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,MAAM,EAKNC,YAAY,EACZC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,cAAc,QACT,eAAe;AACtB,SAASC,SAAS,EAAEC,GAAG,QAAQ,qBAAqB;AACpD,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,aAAa;AACnE,SAASC,KAAK,QAAQ,iBAAiB;;AAEvC;AACA,OAAOC,UAAU,MAAM,oBAAoB;;AAE3C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc,EAAE,EAAEC;EAAY,CAAC,GAAGd,SAAS,CAAiB,CAAC;EACvD,MAAMe,WAAW,GAAGZ,cAAc,CAAC,CAAC;EAEpC,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAiB;IACvDmC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,CAAC;IACXC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACrDC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG5C,QAAQ,CAAM,IAAI,CAAC;EAC7D,MAAM,CAAC6C,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAyB,CAAC,CAAC,CAAC;;EAEhE;EACA,MAAM;IAAE+C,IAAI,EAAEC,MAAM;IAAEC,SAAS,EAAEC,aAAa;IAAEC,KAAK,EAAEC;EAAY,CAAC,GAAGjC,QAAQ,CAC7E,QAAQ,EACR,MAAMG,UAAU,CAAC+B,SAAS,CAAC,CAAC,EAC5B;IACEC,KAAK,EAAE,CAAC;IACRC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAE;EAC5B,CACF,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGzD,KAAK,CAAC0D,OAAO,CAAC,MAAM;IACvCC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEX,MAAM,CAAC,CAAC,CAAC;;IAErC,IAAI,CAACA,MAAM,EAAE;MACXU,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACb,MAAM,CAAC,EAAE;MAC1BU,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,OAAOX,MAAM,EAAEA,MAAM,CAAC;MAC7D,OAAO,EAAE;IACX;IAEAU,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEX,MAAM,CAACc,MAAM,CAAC;IAClD,OAAOd,MAAM;EACf,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAM;IAAED,IAAI,EAAEgB;EAAU,CAAC,GAAG5C,QAAQ,CAClC,CAAC,WAAW,EAAEY,WAAW,CAAC,EAC1B,MAAMT,UAAU,CAAC0C,YAAY,CAACjC,WAAY,CAAC,EAC3C;IACEkC,OAAO,EAAE,CAAC,CAAClC;EACb,CACF,CAAC;EAED,MAAMmC,kBAAkB,GAAGhD,WAAW,CACnC6B,IAAoB,IAAK;IACxB,IAAI,CAAChB,WAAW,EAAE,MAAM,IAAIoC,KAAK,CAAC,0BAA0B,CAAC;IAC7D,OAAO7C,UAAU,CAAC8C,aAAa,CAACrC,WAAW,EAAEgB,IAAI,CAAC;EACpD,CAAC,EACD;IACEsB,SAAS,EAAEA,CAAA,KAAM;MACfhD,KAAK,CAACiD,OAAO,CAAC,6BAA6B,CAAC;MAC5CtC,WAAW,CAACuC,iBAAiB,CAAC,CAAC,WAAW,EAAExC,WAAW,CAAC,CAAC;MACzDC,WAAW,CAACuC,iBAAiB,CAAC,CAAC,UAAU,EAAExC,WAAW,CAAC,CAAC;MACxDF,QAAQ,CAAC,eAAeE,WAAW,EAAE,CAAC;IACxC,CAAC;IACDyC,OAAO,EAAGrB,KAAU,IAAK;MAAA,IAAAsB,eAAA;MACvBf,OAAO,CAACP,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,KAAAsB,eAAA,GAAItB,KAAK,CAACuB,QAAQ,cAAAD,eAAA,eAAdA,eAAA,CAAgB1B,IAAI,EAAE;QACxBD,SAAS,CAACK,KAAK,CAACuB,QAAQ,CAAC3B,IAAI,CAAC;MAChC,CAAC,MAAM;QACL1B,KAAK,CAAC8B,KAAK,CAAC,0CAA0C,CAAC;MACzD;IACF;EACF,CACF,CAAC;EAED,MAAMwB,iBAAiB,GAAIC,KAA2B,IACpDC,KAA0C,IACvC;IACH,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACC,IAAI,KAAK,QAAQ,GAAGC,UAAU,CAACJ,KAAK,CAACE,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IACvG5C,WAAW,CAACgD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACN,KAAK,GAAGE;IACX,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIF,KAAK,IAAI/B,MAAM,EAAE;MACnBC,SAAS,CAACoC,IAAI,IAAI;QAChB,MAAMC,SAAS,GAAG;UAAE,GAAGD;QAAK,CAAC;QAC7B,OAAOC,SAAS,CAACP,KAAK,CAAC;QACvB,OAAOO,SAAS;MAClB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAACP,KAAU,EAAEQ,QAAa,KAAK;IACvD3B,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE0B,QAAQ,CAAC,CAAC,CAAC;;IAExCzC,gBAAgB,CAACyC,QAAQ,CAAC;IAC1BnD,WAAW,CAACgD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP/C,QAAQ,EAAE,CAAAkD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEvD,EAAE,KAAI;IAC5B,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIe,MAAM,CAACV,QAAQ,EAAE;MACnBW,SAAS,CAACoC,IAAI,IAAI;QAChB,MAAMC,SAAS,GAAG;UAAE,GAAGD;QAAK,CAAC;QAC7B,OAAOC,SAAS,CAAChD,QAAQ;QACzB,OAAOgD,SAAS;MAClB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMG,YAAY,GAAIT,KAAsB,IAAK;IAC/CA,KAAK,CAACU,cAAc,CAAC,CAAC;;IAEtB;IACA,MAAMJ,SAAiC,GAAG,CAAC,CAAC;IAE5C,IAAI,CAAClD,QAAQ,CAACE,QAAQ,EAAE;MACtBgD,SAAS,CAAChD,QAAQ,GAAG,wBAAwB;IAC/C;IAEA,IAAIF,QAAQ,CAACG,QAAQ,IAAI,CAAC,EAAE;MAC1B+C,SAAS,CAAC/C,QAAQ,GAAG,iCAAiC;IACxD;IAEA,IAAIH,QAAQ,CAACI,cAAc,IAAI,CAAC,EAAE;MAChC8C,SAAS,CAAC9C,cAAc,GAAG,uCAAuC;IACpE;IAEA,IAAI,CAACJ,QAAQ,CAACK,aAAa,EAAE;MAC3B6C,SAAS,CAAC7C,aAAa,GAAG,2BAA2B;IACvD;IAEA,IAAIkD,MAAM,CAACC,IAAI,CAACN,SAAS,CAAC,CAACrB,MAAM,GAAG,CAAC,EAAE;MACrChB,SAAS,CAACqC,SAAS,CAAC;MACpB;IACF;IAEAjB,kBAAkB,CAACwB,MAAM,CAACzD,QAAQ,CAAC;EACrC,CAAC;EAED,MAAM0D,mBAAmB,GAAGA,CAAA,KAAM;IAChC,OAAO1D,QAAQ,CAACG,QAAQ,GAAGH,QAAQ,CAACI,cAAc;EACpD,CAAC;EAED,IAAI,CAACN,WAAW,EAAE;IAChB,oBACEP,OAAA,CAACrB,GAAG;MAACyF,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,EAAE,EAAE,MAAM;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,eAC3CxE,OAAA,CAACpB,UAAU;QAAC6F,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,OAAO;QAAAF,QAAA,EAAC;MAEvC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACE9E,OAAA,CAAAE,SAAA;IAAAsE,QAAA,gBACExE,OAAA,CAACT,MAAM;MAAAiF,QAAA,gBACLxE,OAAA;QAAAwE,QAAA,EAAO;MAAwB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACvC9E,OAAA;QAAM+E,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAAqC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,eAET9E,OAAA,CAACrB,GAAG;MAACyF,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,EAAE,EAAE,MAAM;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAE3CxE,OAAA,CAACrB,GAAG;QAACsG,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,EAAE,EAAE,CAAE;QAAAX,QAAA,gBAC5CxE,OAAA,CAACjB,MAAM;UACLqG,SAAS,eAAEpF,OAAA,CAACX,SAAS;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBO,OAAO,EAAEA,CAAA,KAAMhF,QAAQ,CAAC,eAAeE,WAAW,EAAE,CAAE;UACtD6D,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,EACf;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9E,OAAA,CAACrB,GAAG;UAAA6F,QAAA,gBACFxE,OAAA,CAACpB,UAAU;YAAC6F,OAAO,EAAC,IAAI;YAACc,SAAS,EAAC,IAAI;YAAAf,QAAA,EAAC;UAExC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZvC,SAAS,iBACRvC,OAAA,CAACpB,UAAU;YAAC6F,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,GAAC,KAC9C,EAACjC,SAAS,CAACwC,IAAI;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9E,OAAA,CAACnB,KAAK;QAACuF,EAAE,EAAE;UAAEG,CAAC,EAAE;QAAE,CAAE;QAAAC,QAAA,eAClBxE,OAAA;UAAMwF,QAAQ,EAAE1B,YAAa;UAAAU,QAAA,eAC3BxE,OAAA,CAACb,IAAI;YAACsG,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAlB,QAAA,gBAEzBxE,OAAA,CAACb,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAApB,QAAA,EACf5C,WAAW,gBACV5B,OAAA,CAACf,KAAK;gBAAC4G,QAAQ,EAAC,OAAO;gBAACzB,EAAE,EAAE;kBAAEe,EAAE,EAAE;gBAAE,CAAE;gBAAAX,QAAA,GAAC,4FAErC,eAAAxE,OAAA;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN9E,OAAA;kBAAAwE,QAAA,GAAO,SAAO,EAAC,CAAA5C,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEkE,OAAO,KAAI,eAAe;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,GACN,CAACpD,aAAa,IAAIM,YAAY,CAACM,MAAM,KAAK,CAAC,gBAC7CtC,OAAA,CAACf,KAAK;gBAAC4G,QAAQ,EAAC,SAAS;gBAACzB,EAAE,EAAE;kBAAEe,EAAE,EAAE;gBAAE,CAAE;gBAAAX,QAAA,EAAC;cAEzC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,gBAER9E,OAAA,CAAChB,YAAY;gBACX+G,OAAO,EAAE/D,YAAa;gBACtBgE,cAAc,EAAGC,MAAM,IAAK;kBAC1B,IAAI,CAACA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE,OAAO,EAAE;kBACpD,OAAO,GAAGA,MAAM,CAACC,MAAM,IAAI,KAAK,MAAMD,MAAM,CAAClB,IAAI,IAAI,eAAe,EAAE;gBACxE,CAAE;gBACFzB,KAAK,EAAEnC,aAAc;gBACrBgF,QAAQ,EAAEvC,iBAAkB;gBAC5BwC,OAAO,EAAE1E,aAAc;gBACvB2E,QAAQ,EAAE3E,aAAa,IAAI,CAAC,CAACE,WAAW,IAAII,YAAY,CAACM,MAAM,KAAK,CAAE;gBACtEgE,oBAAoB,EAAEA,CAACL,MAAM,EAAE3C,KAAK,KAAK;kBACvC,IAAI,CAAC2C,MAAM,IAAI,CAAC3C,KAAK,EAAE,OAAO,KAAK;kBACnC,OAAO2C,MAAM,CAAC3F,EAAE,KAAKgD,KAAK,CAAChD,EAAE;gBAC/B,CAAE;gBACFiG,aAAa,EAAE7E,aAAa,GAAG,YAAY,GAAG,iBAAkB;gBAChE8E,WAAW,EAAGC,MAAM,iBAClBzG,OAAA,CAAClB,SAAS;kBAAA,GACJ2H,MAAM;kBACVC,KAAK,EAAC,cAAc;kBACpB/E,KAAK,EAAE,CAAC,CAACN,MAAM,CAACV,QAAS;kBACzBgG,UAAU,EACRtF,MAAM,CAACV,QAAQ,KACde,aAAa,GAAG,mBAAmB,GACnCM,YAAY,CAACM,MAAM,KAAK,CAAC,GAAG,qBAAqB,GACjD,GAAGN,YAAY,CAACM,MAAM,mBAAmB,CAC3C;kBACDsE,QAAQ;kBACRC,UAAU,EAAE;oBACV,GAAGJ,MAAM,CAACI,UAAU;oBACpBC,YAAY,eACV9G,OAAA,CAAAE,SAAA;sBAAAsE,QAAA,GACG9C,aAAa,gBAAG1B,OAAA,CAACd,gBAAgB;wBAACwF,KAAK,EAAC,SAAS;wBAACqC,IAAI,EAAE;sBAAG;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,GAAG,IAAI,EACrE2B,MAAM,CAACI,UAAU,CAACC,YAAY;oBAAA,eAC/B;kBAEN;gBAAE;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACD;gBACFkC,YAAY,EAAEA,CAACC,KAAK,EAAEhB,MAAM,kBAC1BvH,cAAA,CAACC,GAAG;kBAAC4G,SAAS,EAAC,IAAI;kBAAA,GAAK0B,KAAK;kBAAEC,GAAG,EAAEjB,MAAM,CAAC3F,EAAE,IAAI6G,IAAI,CAACC,MAAM,CAAC,CAAE;kBAAAC,MAAA;kBAAAC,QAAA;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBAAA,gBAC7D9E,OAAA,CAACrB,GAAG;kBAAA6F,QAAA,gBACFxE,OAAA,CAACpB,UAAU;oBAAC6F,OAAO,EAAC,OAAO;oBAAAD,QAAA,GACxByB,MAAM,CAACC,MAAM,IAAI,KAAK,EAAC,KAAG,EAACD,MAAM,CAAClB,IAAI,IAAI,eAAe;kBAAA;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACb9E,OAAA,CAACpB,UAAU;oBAAC6F,OAAO,EAAC,OAAO;oBAACC,KAAK,EAAC,gBAAgB;oBAAAF,QAAA,GAC/CyB,MAAM,CAACsB,UAAU,IAAI,SAAS,EAAC,UAAG,EAACtB,MAAM,CAACuB,MAAM,IAAI,KAAK;kBAAA;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CACF;cACL;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAGP9E,OAAA,CAACb,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAAjD,QAAA,eACvBxE,OAAA,CAAClB,SAAS;gBACR4H,KAAK,EAAC,UAAU;gBAChBlD,IAAI,EAAC,QAAQ;gBACbF,KAAK,EAAE7C,QAAQ,CAACG,QAAQ,IAAI,EAAG;gBAC/BuF,QAAQ,EAAEhD,iBAAiB,CAAC,UAAU,CAAE;gBACxCxB,KAAK,EAAE,CAAC,CAACN,MAAM,CAACT,QAAS;gBACzB+F,UAAU,EAAEtF,MAAM,CAACT,QAAS;gBAC5BgG,QAAQ;gBACRc,SAAS;gBACTC,UAAU,EAAE;kBAAEC,GAAG,EAAE,CAAC;kBAAEC,IAAI,EAAE;gBAAK;cAAE;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGP9E,OAAA,CAACb,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAAjD,QAAA,eACvBxE,OAAA,CAAClB,SAAS;gBACR4H,KAAK,EAAC,gBAAgB;gBACtBlD,IAAI,EAAC,QAAQ;gBACbF,KAAK,EAAE7C,QAAQ,CAACI,cAAc,IAAI,EAAG;gBACrCsF,QAAQ,EAAEhD,iBAAiB,CAAC,gBAAgB,CAAE;gBAC9CxB,KAAK,EAAE,CAAC,CAACN,MAAM,CAACR,cAAe;gBAC/B8F,UAAU,EAAEtF,MAAM,CAACR,cAAe;gBAClC+F,QAAQ;gBACRc,SAAS;gBACTb,UAAU,EAAE;kBACViB,cAAc,eAAE9H,OAAA,CAACZ,cAAc;oBAAC2I,QAAQ,EAAC,OAAO;oBAAAvD,QAAA,EAAC;kBAAC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAgB;gBACpE,CAAE;gBACF6C,UAAU,EAAE;kBAAEC,GAAG,EAAE,CAAC;kBAAEC,IAAI,EAAE;gBAAK;cAAE;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGP9E,OAAA,CAACb,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAAjD,QAAA,eACvBxE,OAAA,CAAClB,SAAS;gBACR4H,KAAK,EAAC,eAAe;gBACrBlD,IAAI,EAAC,MAAM;gBACXF,KAAK,EAAE7C,QAAQ,CAACK,aAAc;gBAC9BqF,QAAQ,EAAEhD,iBAAiB,CAAC,eAAe,CAAE;gBAC7CxB,KAAK,EAAE,CAAC,CAACN,MAAM,CAACP,aAAc;gBAC9B6F,UAAU,EAAEtF,MAAM,CAACP,aAAc;gBACjC8F,QAAQ;gBACRc,SAAS;gBACTM,eAAe,EAAE;kBACfC,MAAM,EAAE;gBACV;cAAE;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGP9E,OAAA,CAACb,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAAjD,QAAA,eACvBxE,OAAA,CAAClB,SAAS;gBACR4H,KAAK,EAAC,aAAa;gBACnBpD,KAAK,EAAE,IAAIa,mBAAmB,CAAC,CAAC,CAAC+D,OAAO,CAAC,CAAC,CAAC,EAAG;gBAC9CR,SAAS;gBACTrB,QAAQ;gBACRQ,UAAU,EAAE;kBACVsB,QAAQ,EAAE;gBACZ;cAAE;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGP9E,OAAA,CAACb,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAApB,QAAA,eAChBxE,OAAA,CAAClB,SAAS;gBACR4H,KAAK,EAAC,kBAAkB;gBACxBpD,KAAK,EAAE7C,QAAQ,CAACS,KAAM;gBACtBiF,QAAQ,EAAEhD,iBAAiB,CAAC,OAAO,CAAE;gBACrCiF,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRX,SAAS;gBACTY,WAAW,EAAC;cAAqC;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGNd,MAAM,CAACC,IAAI,CAAC5C,MAAM,CAAC,CAACiB,MAAM,GAAG,CAAC,iBAC7BtC,OAAA,CAACb,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAApB,QAAA,eAChBxE,OAAA,CAACf,KAAK;gBAAC4G,QAAQ,EAAC,OAAO;gBAAArB,QAAA,EAAC;cAExB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACP,eAGD9E,OAAA,CAACb,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAApB,QAAA,eAChBxE,OAAA,CAACrB,GAAG;gBAACsG,OAAO,EAAC,MAAM;gBAACsD,cAAc,EAAC,UAAU;gBAACC,GAAG,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAAjE,QAAA,gBAC1DxE,OAAA,CAACjB,MAAM;kBACL0F,OAAO,EAAC,UAAU;kBAClBY,OAAO,EAAEA,CAAA,KAAMhF,QAAQ,CAAC,eAAeE,WAAW,EAAE,CAAE;kBACtD8F,QAAQ,EAAE3D,kBAAkB,CAACjB,SAAU;kBAAA+C,QAAA,EACxC;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9E,OAAA,CAACjB,MAAM;kBACLyE,IAAI,EAAC,QAAQ;kBACbiB,OAAO,EAAC,WAAW;kBACnBW,SAAS,EAAE1C,kBAAkB,CAACjB,SAAS,gBAAGzB,OAAA,CAACd,gBAAgB;oBAAC6H,IAAI,EAAE;kBAAG;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAG9E,OAAA,CAACV,GAAG;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnFuB,QAAQ,EAAE3D,kBAAkB,CAACjB,SAAU;kBAAA+C,QAAA,EAEtC9B,kBAAkB,CAACjB,SAAS,GAAG,WAAW,GAAG;gBAAa;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGR9E,OAAA,CAACnB,KAAK;QAACuF,EAAE,EAAE;UAAEG,CAAC,EAAE,CAAC;UAAEkE,EAAE,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAqB,CAAE;QAAAlE,QAAA,gBACxDxE,OAAA,CAACpB,UAAU;UAAC6F,OAAO,EAAC,IAAI;UAACkE,YAAY;UAAAnE,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb9E,OAAA,CAACpB,UAAU;UAAC6F,OAAO,EAAC,OAAO;UAACC,KAAK,EAAC,gBAAgB;UAAAF,QAAA,EAAC;QAEnD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb9E,OAAA,CAACrB,GAAG;UAAC4G,SAAS,EAAC,IAAI;UAACnB,EAAE,EAAE;YAAEqE,EAAE,EAAE,CAAC;YAAEG,EAAE,EAAE;UAAE,CAAE;UAAApE,QAAA,gBACvCxE,OAAA,CAACpB,UAAU;YAAC2G,SAAS,EAAC,IAAI;YAACd,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,EAAC;UAElE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9E,OAAA,CAACpB,UAAU;YAAC2G,SAAS,EAAC,IAAI;YAACd,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,EAAC;UAElE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9E,OAAA,CAACpB,UAAU;YAAC2G,SAAS,EAAC,IAAI;YAACd,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,EAAC;UAElE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9E,OAAA,CAACpB,UAAU;YAAC2G,SAAS,EAAC,IAAI;YAACd,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,EAAC;UAElE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAC1E,EAAA,CAzYID,cAAwB;EAAA,QACXX,WAAW,EACAC,SAAS,EACjBG,cAAc,EAcqCD,QAAQ,EA4BnDA,QAAQ,EAQTD,WAAW;AAAA;AAAAmJ,EAAA,GArDlC1I,cAAwB;AA2Y9B,eAAeA,cAAc;AAAC,IAAA0I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}