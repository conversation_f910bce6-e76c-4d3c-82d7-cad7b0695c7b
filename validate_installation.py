#!/usr/bin/env python3

"""
TrustVault - Installation Validation Script

This script validates that TrustVault is properly installed and all
key features are working correctly.
"""

import requests
import json
import time
import sys
from urllib.parse import urljoin

class TrustVaultValidator:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.api_url = urljoin(base_url, "/api/v1/")
        self.session = requests.Session()
        self.access_token = None
        self.test_user_email = "<EMAIL>"
        self.test_user_password = "TestPass123!"
        
    def print_header(self, title):
        """Print a formatted header."""
        print("\n" + "="*60)
        print(f" {title}")
        print("="*60)

    def print_test(self, test_name, success, message=""):
        """Print test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")

    def make_request(self, method, endpoint, **kwargs):
        """Make API request with proper headers."""
        url = urljoin(self.api_url, endpoint)
        headers = kwargs.get('headers', {})
        
        if self.access_token:
            headers['Authorization'] = f'Bearer {self.access_token}'
        
        kwargs['headers'] = headers
        
        try:
            response = self.session.request(method, url, **kwargs)
            return response
        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            return None

    def test_health_check(self):
        """Test system health endpoints."""
        self.print_header("🏥 Health Check Tests")
        
        # Test backend health
        try:
            response = requests.get(f"{self.base_url}/health/", timeout=10)
            success = response.status_code == 200
            self.print_test("Backend Health Check", success, 
                          f"Status: {response.status_code}")
        except Exception as e:
            self.print_test("Backend Health Check", False, str(e))
        
        # Test API root
        try:
            response = requests.get(self.api_url, timeout=10)
            success = response.status_code == 200
            self.print_test("API Root Endpoint", success,
                          f"Status: {response.status_code}")
        except Exception as e:
            self.print_test("API Root Endpoint", False, str(e))
        
        # Test frontend (if available)
        try:
            frontend_url = "http://localhost:3000"
            response = requests.get(frontend_url, timeout=10)
            success = response.status_code == 200
            self.print_test("Frontend Health Check", success,
                          f"Status: {response.status_code}")
        except Exception as e:
            self.print_test("Frontend Health Check", False, 
                          "Frontend not accessible (this is OK for backend-only testing)")

    def test_authentication(self):
        """Test authentication endpoints."""
        self.print_header("🔐 Authentication Tests")
        
        # Test user registration
        register_data = {
            "email": self.test_user_email,
            "username": "testuser",
            "first_name": "Test",
            "last_name": "User",
            "password": self.test_user_password,
            "password_confirm": self.test_user_password
        }
        
        response = self.make_request("POST", "auth/register/", json=register_data)
        if response:
            success = response.status_code in [201, 400]  # 400 if user already exists
            self.print_test("User Registration", success,
                          f"Status: {response.status_code}")
        else:
            self.print_test("User Registration", False, "Request failed")
        
        # Test user login
        login_data = {
            "email": self.test_user_email,
            "password": self.test_user_password
        }
        
        response = self.make_request("POST", "auth/login/", json=login_data)
        if response and response.status_code == 200:
            try:
                data = response.json()
                self.access_token = data.get('access')
                success = bool(self.access_token)
                self.print_test("User Login", success,
                              "Access token received" if success else "No access token")
            except json.JSONDecodeError:
                self.print_test("User Login", False, "Invalid JSON response")
        else:
            # Try with default admin credentials
            admin_login_data = {
                "email": "<EMAIL>",
                "password": "admin123"
            }
            
            response = self.make_request("POST", "auth/login/", json=admin_login_data)
            if response and response.status_code == 200:
                try:
                    data = response.json()
                    self.access_token = data.get('access')
                    success = bool(self.access_token)
                    self.print_test("Admin Login", success,
                                  "Using default admin credentials")
                except json.JSONDecodeError:
                    self.print_test("User Login", False, "Invalid JSON response")
            else:
                self.print_test("User Login", False, 
                              f"Status: {response.status_code if response else 'No response'}")
        
        # Test profile access
        if self.access_token:
            response = self.make_request("GET", "auth/profile/")
            if response:
                success = response.status_code == 200
                self.print_test("Profile Access", success,
                              f"Status: {response.status_code}")
            else:
                self.print_test("Profile Access", False, "Request failed")

    def test_portfolio_management(self):
        """Test portfolio management endpoints."""
        self.print_header("📊 Portfolio Management Tests")
        
        if not self.access_token:
            self.print_test("Portfolio Tests", False, "No access token available")
            return
        
        # Test portfolio list
        response = self.make_request("GET", "portfolio/")
        if response:
            success = response.status_code == 200
            self.print_test("List Portfolios", success,
                          f"Status: {response.status_code}")
            
            if success:
                try:
                    portfolios = response.json()
                    self.print_test("Portfolio Data Structure", True,
                                  f"Found {len(portfolios)} portfolios")
                except json.JSONDecodeError:
                    self.print_test("Portfolio Data Structure", False, "Invalid JSON")
        else:
            self.print_test("List Portfolios", False, "Request failed")
        
        # Test portfolio creation
        portfolio_data = {
            "name": "Test Portfolio",
            "description": "Validation test portfolio",
            "currency": "USD"
        }
        
        response = self.make_request("POST", "portfolio/", json=portfolio_data)
        if response:
            success = response.status_code == 201
            self.print_test("Create Portfolio", success,
                          f"Status: {response.status_code}")
            
            if success:
                try:
                    portfolio = response.json()
                    portfolio_id = portfolio.get('id')
                    if portfolio_id:
                        self.test_portfolio_details(portfolio_id)
                except json.JSONDecodeError:
                    self.print_test("Portfolio Creation Data", False, "Invalid JSON")
        else:
            self.print_test("Create Portfolio", False, "Request failed")

    def test_portfolio_details(self, portfolio_id):
        """Test portfolio detail endpoints."""
        # Test portfolio detail
        response = self.make_request("GET", f"portfolio/{portfolio_id}/")
        if response:
            success = response.status_code == 200
            self.print_test("Portfolio Details", success,
                          f"Status: {response.status_code}")
        else:
            self.print_test("Portfolio Details", False, "Request failed")
        
        # Test portfolio analytics
        response = self.make_request("GET", f"portfolio/{portfolio_id}/analytics/")
        if response:
            success = response.status_code == 200
            self.print_test("Portfolio Analytics", success,
                          f"Status: {response.status_code}")
        else:
            self.print_test("Portfolio Analytics", False, "Request failed")
        
        # Test holdings
        response = self.make_request("GET", f"portfolio/{portfolio_id}/holdings/")
        if response:
            success = response.status_code == 200
            self.print_test("Portfolio Holdings", success,
                          f"Status: {response.status_code}")
        else:
            self.print_test("Portfolio Holdings", False, "Request failed")
        
        # Test transactions
        response = self.make_request("GET", f"portfolio/{portfolio_id}/transactions/")
        if response:
            success = response.status_code == 200
            self.print_test("Portfolio Transactions", success,
                          f"Status: {response.status_code}")
        else:
            self.print_test("Portfolio Transactions", False, "Request failed")

    def test_asset_management(self):
        """Test asset management endpoints."""
        self.print_header("🏦 Asset Management Tests")
        
        if not self.access_token:
            self.print_test("Asset Tests", False, "No access token available")
            return
        
        # Test asset list
        response = self.make_request("GET", "portfolio/assets/")
        if response:
            success = response.status_code == 200
            self.print_test("List Assets", success,
                          f"Status: {response.status_code}")
            
            if success:
                try:
                    assets = response.json()
                    self.print_test("Asset Data Structure", True,
                                  f"Found {len(assets)} assets")
                except json.JSONDecodeError:
                    self.print_test("Asset Data Structure", False, "Invalid JSON")
        else:
            self.print_test("List Assets", False, "Request failed")

    def test_security_features(self):
        """Test security endpoints."""
        self.print_header("🛡️ Security Feature Tests")
        
        if not self.access_token:
            self.print_test("Security Tests", False, "No access token available")
            return
        
        # Test security dashboard
        response = self.make_request("GET", "security/dashboard/")
        if response:
            success = response.status_code == 200
            self.print_test("Security Dashboard", success,
                          f"Status: {response.status_code}")
        else:
            self.print_test("Security Dashboard", False, "Request failed")
        
        # Test audit logs
        response = self.make_request("GET", "security/audit-logs/")
        if response:
            success = response.status_code == 200
            self.print_test("Audit Logs", success,
                          f"Status: {response.status_code}")
        else:
            self.print_test("Audit Logs", False, "Request failed")
        
        # Test security events
        response = self.make_request("GET", "security/events/")
        if response:
            success = response.status_code == 200
            self.print_test("Security Events", success,
                          f"Status: {response.status_code}")
        else:
            self.print_test("Security Events", False, "Request failed")

    def test_api_documentation(self):
        """Test API documentation endpoints."""
        self.print_header("📚 API Documentation Tests")
        
        # Test OpenAPI schema
        try:
            response = requests.get(f"{self.base_url}/api/schema/", timeout=10)
            success = response.status_code == 200
            self.print_test("OpenAPI Schema", success,
                          f"Status: {response.status_code}")
        except Exception as e:
            self.print_test("OpenAPI Schema", False, str(e))
        
        # Test Swagger UI
        try:
            response = requests.get(f"{self.base_url}/api/docs/", timeout=10)
            success = response.status_code == 200
            self.print_test("Swagger UI", success,
                          f"Status: {response.status_code}")
        except Exception as e:
            self.print_test("Swagger UI", False, str(e))

    def run_all_tests(self):
        """Run all validation tests."""
        print("🧪 TrustVault Installation Validation")
        print("=====================================")
        print(f"Testing against: {self.base_url}")
        print(f"API URL: {self.api_url}")
        
        start_time = time.time()
        
        # Run all test suites
        self.test_health_check()
        self.test_authentication()
        self.test_portfolio_management()
        self.test_asset_management()
        self.test_security_features()
        self.test_api_documentation()
        
        # Summary
        end_time = time.time()
        duration = end_time - start_time
        
        self.print_header("📋 Validation Summary")
        print(f"⏱️  Total Duration: {duration:.2f} seconds")
        print(f"🔗 Base URL: {self.base_url}")
        print(f"🔑 Authentication: {'✅ Working' if self.access_token else '❌ Failed'}")
        
        if self.access_token:
            print("\n🎉 TrustVault appears to be working correctly!")
            print("\n📖 Next Steps:")
            print("1. Access the frontend at http://localhost:3000")
            print("2. <NAME_EMAIL> / admin123")
            print("3. Explore the API documentation at http://localhost:8000/api/docs/")
            print("4. Change default passwords for production use")
        else:
            print("\n⚠️  Some issues were detected. Please check the logs above.")
            print("\n🔧 Troubleshooting:")
            print("1. Ensure all services are running: docker-compose ps")
            print("2. Check service logs: docker-compose logs")
            print("3. Verify database migrations: docker-compose exec backend python manage.py migrate")

def main():
    """Main validation function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Validate TrustVault installation')
    parser.add_argument('--url', default='http://localhost:8000',
                       help='Base URL for TrustVault backend (default: http://localhost:8000)')
    
    args = parser.parse_args()
    
    validator = TrustVaultValidator(args.url)
    validator.run_all_tests()

if __name__ == "__main__":
    main()
