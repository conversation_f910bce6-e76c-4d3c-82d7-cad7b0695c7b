[{"C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\utils\\security.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\store\\authStore.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\NotFoundPage.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\LoginPage.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\RegisterPage.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Dashboard\\DashboardPage.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfoliosPage.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfolioDetailPage.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Security\\SecurityPage.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Profile\\ProfilePage.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Auth\\ProtectedRoute.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Layout\\Layout.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\services\\api.ts": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\CreatePortfolioPage.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\AddHoldingPage.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\TransactionsPage.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\AnalyticsPage.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Settings\\SettingsPage.tsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\ErrorBoundary\\index.ts": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx": "22"}, {"size": 2864, "mtime": 1753617893661, "results": "23", "hashOfConfig": "24"}, {"size": 4384, "mtime": 1753713773810, "results": "25", "hashOfConfig": "24"}, {"size": 8283, "mtime": 1753621069166, "results": "26", "hashOfConfig": "24"}, {"size": 5460, "mtime": 1753617989103, "results": "27", "hashOfConfig": "24"}, {"size": 1980, "mtime": 1753618351980, "results": "28", "hashOfConfig": "24"}, {"size": 6779, "mtime": 1753618095645, "results": "29", "hashOfConfig": "24"}, {"size": 14205, "mtime": 1753621205385, "results": "30", "hashOfConfig": "24"}, {"size": 11100, "mtime": 1753618211292, "results": "31", "hashOfConfig": "24"}, {"size": 6875, "mtime": 1753618236618, "results": "32", "hashOfConfig": "24"}, {"size": 10774, "mtime": 1753710814608, "results": "33", "hashOfConfig": "24"}, {"size": 11860, "mtime": 1753621571203, "results": "34", "hashOfConfig": "24"}, {"size": 10407, "mtime": 1753621449358, "results": "35", "hashOfConfig": "24"}, {"size": 1043, "mtime": 1753618067250, "results": "36", "hashOfConfig": "24"}, {"size": 7218, "mtime": 1753618170921, "results": "37", "hashOfConfig": "24"}, {"size": 8471, "mtime": 1753713622593, "results": "38", "hashOfConfig": "24"}, {"size": 8492, "mtime": 1753711056467, "results": "39", "hashOfConfig": "24"}, {"size": 11320, "mtime": 1753711041407, "results": "40", "hashOfConfig": "24"}, {"size": 11677, "mtime": 1753710882340, "results": "41", "hashOfConfig": "24"}, {"size": 13650, "mtime": 1753713637850, "results": "42", "hashOfConfig": "24"}, {"size": 13099, "mtime": 1753713351388, "results": "43", "hashOfConfig": "24"}, {"size": 118, "mtime": 1753713714543, "results": "44", "hashOfConfig": "24"}, {"size": 5770, "mtime": 1753713706774, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15o5uiy", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\utils\\security.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\store\\authStore.ts", ["112"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\NotFoundPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Dashboard\\DashboardPage.tsx", ["113"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfoliosPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfolioDetailPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Security\\SecurityPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Profile\\ProfilePage.tsx", ["114"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Layout\\Layout.tsx", ["115"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\services\\api.ts", ["116"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\CreatePortfolioPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\AddHoldingPage.tsx", ["117", "118", "119", "120"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\TransactionsPage.tsx", ["121", "122"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\AnalyticsPage.tsx", ["123", "124"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Settings\\SettingsPage.tsx", ["125", "126", "127", "128", "129", "130"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\ErrorBoundary\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx", [], [], {"ruleId": "131", "severity": 1, "message": "132", "line": 6, "column": 16, "nodeType": "133", "messageId": "134", "endLine": 6, "endColumn": 26}, {"ruleId": "131", "severity": 1, "message": "135", "line": 10, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 10, "endColumn": 8}, {"ruleId": "131", "severity": 1, "message": "136", "line": 32, "column": 10, "nodeType": "133", "messageId": "134", "endLine": 32, "endColumn": 14}, {"ruleId": "131", "severity": 1, "message": "137", "line": 33, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 33, "endColumn": 14}, {"ruleId": "131", "severity": 1, "message": "138", "line": 17, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 17, "endColumn": 11}, {"ruleId": "131", "severity": 1, "message": "139", "line": 10, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 10, "endColumn": 14}, {"ruleId": "131", "severity": 1, "message": "140", "line": 11, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 11, "endColumn": 13}, {"ruleId": "131", "severity": 1, "message": "141", "line": 12, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 12, "endColumn": 9}, {"ruleId": "131", "severity": 1, "message": "142", "line": 13, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 13, "endColumn": 11}, {"ruleId": "131", "severity": 1, "message": "143", "line": 31, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 31, "endColumn": 13}, {"ruleId": "131", "severity": 1, "message": "144", "line": 45, "column": 10, "nodeType": "133", "messageId": "134", "endLine": 45, "endColumn": 29}, {"ruleId": "131", "severity": 1, "message": "145", "line": 50, "column": 17, "nodeType": "133", "messageId": "134", "endLine": 50, "endColumn": 26}, {"ruleId": "131", "severity": 1, "message": "146", "line": 50, "column": 39, "nodeType": "133", "messageId": "134", "endLine": 50, "endColumn": 55}, {"ruleId": "131", "severity": 1, "message": "147", "line": 14, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 14, "endColumn": 19}, {"ruleId": "131", "severity": 1, "message": "148", "line": 17, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 17, "endColumn": 10}, {"ruleId": "131", "severity": 1, "message": "149", "line": 23, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 23, "endColumn": 13}, {"ruleId": "131", "severity": 1, "message": "150", "line": 34, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 34, "endColumn": 10}, {"ruleId": "131", "severity": 1, "message": "151", "line": 35, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 35, "endColumn": 11}, {"ruleId": "131", "severity": 1, "message": "152", "line": 66, "column": 17, "nodeType": "133", "messageId": "134", "endLine": 66, "endColumn": 30}, "@typescript-eslint/no-unused-vars", "'AuthTokens' is defined but never used.", "Identifier", "unusedVar", "'Paper' is defined but never used.", "'User' is defined but never used.", "'ChevronLeft' is defined but never used.", "'ApiError' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'FilterList' is defined but never used.", "'selectedTransaction' is assigned a value but never used.", "'analytics' is assigned a value but never used.", "'analyticsLoading' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", "'Divider' is defined but never used.", "'IconButton' is defined but never used.", "'Palette' is defined but never used.", "'Language' is defined but never used.", "'updateProfile' is assigned a value but never used."]