#!/usr/bin/env python3

"""
Script de test pour valider tous les modules de sécurité TrustVault
"""

import os
import sys
import django
from datetime import datetime

# Configuration Django
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trustvault.settings')
django.setup()

def test_crypto_module():
    """Test du module de chiffrement avancé"""
    print("🔐 Test du module de chiffrement...")
    
    try:
        from apps.security.advanced_crypto import crypto
        
        # Test de chiffrement/déchiffrement
        test_data = "Données sensibles de test"
        encrypted = crypto.encrypt_data(test_data.encode())
        decrypted = crypto.decrypt_data(encrypted).decode()
        
        assert test_data == decrypted, "Échec du test de chiffrement/déchiffrement"
        
        # Test de génération de token
        token = crypto.generate_secure_token(32)
        assert len(token) > 0, "Échec de la génération de token"
        assert isinstance(token, str), "Token doit être une chaîne"
        
        # Test de hachage
        password = "motdepasse123"
        hashed = crypto.hash_password(password)
        assert crypto.verify_password(password, hashed), "Échec de la vérification de mot de passe"
        
        print("✅ Module de chiffrement : OK")
        return True
        
    except Exception as e:
        print(f"❌ Module de chiffrement : ERREUR - {e}")
        return False

def test_auth_module():
    """Test du module d'authentification avancée"""
    print("🔑 Test du module d'authentification...")
    
    try:
        from apps.security.advanced_auth import auth_system
        
        # Test de génération de code MFA
        secret = auth_system.generate_mfa_secret()
        assert len(secret) == 32, "Échec de la génération de secret MFA"
        
        # Test de validation de force de mot de passe
        weak_password = "123"
        strong_password = "MonMotDePasseTresSecurise123!"
        
        weak_result = auth_system.validate_password_strength(weak_password)
        strong_result = auth_system.validate_password_strength(strong_password)
        
        assert not weak_result['is_strong'], "Mot de passe faible accepté à tort"
        assert strong_result['is_strong'], "Mot de passe fort rejeté à tort"
        
        print("✅ Module d'authentification : OK")
        return True
        
    except Exception as e:
        print(f"❌ Module d'authentification : ERREUR - {e}")
        return False

def test_ids_module():
    """Test du module de détection d'intrusion"""
    print("🚨 Test du module de détection d'intrusion...")
    
    try:
        from apps.security.intrusion_detection import ids
        
        # Test de détection d'injection SQL
        malicious_request = {
            'ip_address': '*************',
            'user_agent': 'Mozilla/5.0',
            'method': 'POST',
            'path': '/api/login/',
            'query_params': {},
            'post_params': {'username': "admin'; DROP TABLE users; --", 'password': 'test'}
        }
        
        result = ids.analyze_request(malicious_request)
        assert result['threat_detected'], "Injection SQL non détectée"
        assert result['risk_score'] > 50, "Score de risque trop faible pour une injection SQL"
        
        # Test de requête normale
        normal_request = {
            'ip_address': '*************',
            'user_agent': 'Mozilla/5.0',
            'method': 'GET',
            'path': '/api/dashboard/',
            'query_params': {},
            'post_params': {}
        }
        
        normal_result = ids.analyze_request(normal_request)
        assert not normal_result['threat_detected'], "Faux positif sur requête normale"
        
        print("✅ Module de détection d'intrusion : OK")
        return True
        
    except Exception as e:
        print(f"❌ Module de détection d'intrusion : ERREUR - {e}")
        return False

def test_compliance_module():
    """Test du module de conformité"""
    print("📋 Test du module de conformité...")
    
    try:
        from apps.security.compliance import compliance_framework
        
        # Test d'évaluation de conformité ISO 27001
        iso_result = compliance_framework.assess_compliance('ISO_27001')
        
        assert 'overall_score' in iso_result, "Score global manquant"
        assert 'compliance_status' in iso_result, "Statut de conformité manquant"
        assert iso_result['overall_score'] >= 0, "Score de conformité invalide"
        assert iso_result['overall_score'] <= 100, "Score de conformité invalide"
        
        # Test d'évaluation GDPR
        gdpr_result = compliance_framework.assess_compliance('GDPR')
        assert gdpr_result['framework'] == 'GDPR', "Framework incorrect"
        
        print("✅ Module de conformité : OK")
        return True
        
    except Exception as e:
        print(f"❌ Module de conformité : ERREUR - {e}")
        return False

def test_penetration_module():
    """Test du module de tests de pénétration"""
    print("🎯 Test du module de tests de pénétration...")
    
    try:
        from apps.security.penetration_testing import penetration_testing_suite
        
        # Test de validation des méthodes de test
        assert hasattr(penetration_testing_suite, 'run_comprehensive_security_assessment'), "Méthode d'évaluation manquante"
        assert hasattr(penetration_testing_suite, '_test_brute_force_protection'), "Test de force brute manquant"
        assert hasattr(penetration_testing_suite, '_test_sql_injection'), "Test d'injection SQL manquant"
        
        # Test de génération de recommandations
        mock_results = {
            'overall_score': 75,
            'critical_issues': ['SQL_INJECTION'],
            'tests_completed': []
        }
        
        recommendations = penetration_testing_suite._generate_security_recommendations(mock_results)
        assert len(recommendations) > 0, "Aucune recommandation générée"
        
        print("✅ Module de tests de pénétration : OK")
        return True
        
    except Exception as e:
        print(f"❌ Module de tests de pénétration : ERREUR - {e}")
        return False

def test_models():
    """Test des modèles de sécurité"""
    print("🗄️ Test des modèles de sécurité...")
    
    try:
        from apps.security.models import (
            ThreatIntelligence, 
            UserSession, 
            DeviceFingerprint, 
            RiskAssessment,
            IncidentResponse,
            ComplianceAudit,
            SecurityMetric,
            SecurityAlert
        )
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Test de création d'un utilisateur de test
        test_user, created = User.objects.get_or_create(
            username='test_security',
            defaults={'email': '<EMAIL>'}
        )
        
        # Test de création d'une intelligence de menace
        threat = ThreatIntelligence.objects.create(
            threat_type='IP_BLACKLIST',
            value='***********',
            description='Test threat intelligence',
            severity='HIGH',
            source='TEST',
            confidence=90
        )
        
        assert threat.id is not None, "Échec de création de ThreatIntelligence"
        assert not threat.is_expired, "ThreatIntelligence marquée comme expirée"
        
        # Test de création d'un incident
        incident = IncidentResponse.objects.create(
            incident_type='INTRUSION_ATTEMPT',
            severity='HIGH',
            title='Test Incident',
            description='Test incident for validation'
        )
        
        assert incident.id is not None, "Échec de création d'IncidentResponse"
        assert incident.status == 'OPEN', "Statut d'incident incorrect"
        
        print("✅ Modèles de sécurité : OK")
        return True
        
    except Exception as e:
        print(f"❌ Modèles de sécurité : ERREUR - {e}")
        return False

def test_middleware():
    """Test du middleware de sécurité"""
    print("🛡️ Test du middleware de sécurité...")
    
    try:
        from apps.security.middleware import SecurityMiddleware
        from django.http import HttpRequest, HttpResponse
        
        # Créer une instance du middleware
        middleware = SecurityMiddleware(lambda request: HttpResponse("OK"))
        
        # Créer une requête de test
        request = HttpRequest()
        request.method = 'GET'
        request.path = '/test/'
        request.META = {
            'REMOTE_ADDR': '*************',
            'HTTP_USER_AGENT': 'Mozilla/5.0 Test Browser'
        }
        
        # Tester le middleware
        response = middleware(request)
        
        assert response.status_code == 200, "Middleware a bloqué une requête normale"
        
        print("✅ Middleware de sécurité : OK")
        return True
        
    except Exception as e:
        print(f"❌ Middleware de sécurité : ERREUR - {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 Démarrage des tests de sécurité TrustVault")
    print("=" * 60)
    
    tests = [
        test_crypto_module,
        test_auth_module,
        test_ids_module,
        test_compliance_module,
        test_penetration_module,
        test_models,
        test_middleware
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Erreur lors du test {test.__name__}: {e}")
            failed += 1
        print()
    
    print("=" * 60)
    print(f"📊 Résultats des tests:")
    print(f"✅ Tests réussis: {passed}")
    print(f"❌ Tests échoués: {failed}")
    print(f"📈 Taux de réussite: {(passed / (passed + failed) * 100):.1f}%")
    
    if failed == 0:
        print("\n🎉 Tous les tests de sécurité sont passés avec succès!")
        print("🛡️ L'infrastructure de sécurité TrustVault est opérationnelle.")
        return True
    else:
        print(f"\n⚠️ {failed} test(s) ont échoué. Vérification nécessaire.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
