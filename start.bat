@echo off
REM TrustVault - Windows Startup Script

echo 🚀 Starting TrustVault Application...
echo ==================================

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose is not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist .env (
    echo ❌ .env file not found. Creating from template...
    if exist .env.example (
        copy .env.example .env
    ) else (
        echo ⚠️  Please create a .env file with your configuration
    )
)

REM Determine environment
set ENVIRONMENT=%1
if "%ENVIRONMENT%"=="" set ENVIRONMENT=development

echo 📋 Environment: %ENVIRONMENT%

REM Start services based on environment
if "%ENVIRONMENT%"=="production" (
    echo 🏭 Starting production environment...
    docker-compose -f docker-compose.yml up -d
) else if "%ENVIRONMENT%"=="development" (
    echo 🛠️  Starting development environment...
    docker-compose -f docker-compose.dev.yml up -d
) else (
    echo ❌ Invalid environment. Use 'development' or 'production'
    pause
    exit /b 1
)

echo.
echo ⏳ Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Check service health
echo 🔍 Checking service health...

REM Check backend
curl -f http://localhost:8000/health/ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Backend is healthy
) else (
    echo ⚠️  Backend health check failed
)

REM Check frontend
curl -f http://localhost:3000/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Frontend is healthy
) else (
    echo ⚠️  Frontend health check failed
)

echo.
echo 🎉 TrustVault is starting up!
echo ==================================
echo 📱 Frontend: http://localhost:3000
echo 🔧 Backend API: http://localhost:8000
echo 📚 API Docs: http://localhost:8000/api/docs/
echo 🔐 Admin Panel: http://localhost:8000/admin/
echo.
echo Default admin credentials:
echo Email: <EMAIL>
echo Password: admin123
echo.
echo 📊 To view logs: docker-compose logs -f
echo 🛑 To stop: docker-compose down
echo ==================================
pause
