{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\pages\\\\Security\\\\SecurityPage.tsx\",\n  _s = $RefreshSig$();\n// TrustVault - Security Page\n\nimport React from 'react';\nimport { Box, Typography, Grid, Card, CardContent, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, Alert } from '@mui/material';\nimport { Security, Warning, CheckCircle, Error } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useQuery } from 'react-query';\n\n// Services\nimport apiService from '../../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SecurityPage = () => {\n  _s();\n  var _securityDashboard$lo, _securityDashboard$lo2, _securityDashboard$lo3;\n  const {\n    data: securityDashboard,\n    isLoading,\n    error: dashboardError\n  } = useQuery('security-dashboard', apiService.getSecurityDashboard, {\n    retry: 1,\n    refetchOnWindowFocus: false\n  });\n  const {\n    data: securityEvents,\n    error: eventsError\n  } = useQuery('security-events', () => apiService.getSecurityEvents({\n    page: 1\n  }), {\n    retry: 1,\n    refetchOnWindowFocus: false\n  });\n  const getRiskLevelColor = level => {\n    switch (level) {\n      case 'CRITICAL':\n        return 'error';\n      case 'HIGH':\n        return 'warning';\n      case 'MEDIUM':\n        return 'info';\n      case 'LOW':\n        return 'success';\n      default:\n        return 'default';\n    }\n  };\n  const getRiskLevelIcon = level => {\n    switch (level) {\n      case 'CRITICAL':\n        return /*#__PURE__*/_jsxDEV(Error, {\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 16\n        }, this);\n      case 'HIGH':\n        return /*#__PURE__*/_jsxDEV(Warning, {\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 16\n        }, this);\n      case 'MEDIUM':\n        return /*#__PURE__*/_jsxDEV(Warning, {\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 16\n        }, this);\n      case 'LOW':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Security, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // Gestion des erreurs\n  if (dashboardError || eventsError) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Helmet, {\n        children: [/*#__PURE__*/_jsxDEV(\"title\", {\n          children: \"Security - TrustVault\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n          name: \"description\",\n          content: \"Security monitoring and events\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: \"Unable to load security data. Please check your connection and try again.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Security - TrustVault\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Security monitoring and events\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        mb: 4,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          gutterBottom: true,\n          children: \"Security Center\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          children: \"Monitor security events and system status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), securityDashboard && securityDashboard.security_events && securityDashboard.security_events.unresolved > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        sx: {\n          mb: 3\n        },\n        children: [\"You have \", securityDashboard.security_events.unresolved, \" unresolved security events that require attention.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        mb: 4,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    color: \"text.secondary\",\n                    gutterBottom: true,\n                    children: \"Total Events\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 130,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    children: (securityDashboard === null || securityDashboard === void 0 ? void 0 : securityDashboard.security_events.total_events) || 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Security, {\n                  color: \"primary\",\n                  sx: {\n                    fontSize: 40\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    color: \"text.secondary\",\n                    gutterBottom: true,\n                    children: \"Last 24h\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    children: (securityDashboard === null || securityDashboard === void 0 ? void 0 : securityDashboard.security_events.last_24h) || 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Warning, {\n                  color: \"warning\",\n                  sx: {\n                    fontSize: 40\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    color: \"text.secondary\",\n                    gutterBottom: true,\n                    children: \"Critical Events\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    color: \"error\",\n                    children: (securityDashboard === null || securityDashboard === void 0 ? void 0 : securityDashboard.security_events.critical) || 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Error, {\n                  color: \"error\",\n                  sx: {\n                    fontSize: 40\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    color: \"text.secondary\",\n                    gutterBottom: true,\n                    children: \"Unresolved\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    color: \"warning\",\n                    children: (securityDashboard === null || securityDashboard === void 0 ? void 0 : securityDashboard.security_events.unresolved) || 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Warning, {\n                  color: \"warning\",\n                  sx: {\n                    fontSize: 40\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        mb: 4,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Login Activity (24h)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    textAlign: \"center\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h3\",\n                      color: \"success.main\",\n                      children: (securityDashboard === null || securityDashboard === void 0 ? void 0 : securityDashboard.login_attempts.successful_last_24h) || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 210,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Successful\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    textAlign: \"center\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h3\",\n                      color: \"error.main\",\n                      children: (securityDashboard === null || securityDashboard === void 0 ? void 0 : securityDashboard.login_attempts.failed_last_24h) || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Failed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), ((_securityDashboard$lo = securityDashboard === null || securityDashboard === void 0 ? void 0 : (_securityDashboard$lo2 = securityDashboard.login_attempts) === null || _securityDashboard$lo2 === void 0 ? void 0 : _securityDashboard$lo2.suspicious_last_24h) !== null && _securityDashboard$lo !== void 0 ? _securityDashboard$lo : 0) > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"warning\",\n                sx: {\n                  mt: 2\n                },\n                children: [securityDashboard === null || securityDashboard === void 0 ? void 0 : (_securityDashboard$lo3 = securityDashboard.login_attempts) === null || _securityDashboard$lo3 === void 0 ? void 0 : _securityDashboard$lo3.suspicious_last_24h, \" suspicious login attempts detected\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Top Threat Sources\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this), securityDashboard !== null && securityDashboard !== void 0 && securityDashboard.threat_sources && securityDashboard.threat_sources.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                children: securityDashboard.threat_sources.slice(0, 5).map((source, index) => /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  py: 1,\n                  borderBottom: index < 4 ? 1 : 0,\n                  borderColor: \"divider\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontFamily: \"monospace\",\n                    children: source.source_ip\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `${source.count} events`,\n                    size: \"small\",\n                    color: \"warning\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 25\n                  }, this)]\n                }, source.source_ip, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"No threat sources detected\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Recent Security Events\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), isLoading ? /*#__PURE__*/_jsxDEV(Typography, {\n            children: \"Loading security events...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this) : securityEvents && securityEvents.results.length > 0 ? /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            variant: \"outlined\",\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Event Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Risk Level\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Source IP\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Description\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: securityEvents.results.slice(0, 10).map(event => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 1,\n                      children: [getRiskLevelIcon(event.risk_level), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: event.event_type.replace('_', ' ')\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 308,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 306,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: event.risk_level,\n                      color: getRiskLevelColor(event.risk_level),\n                      size: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontFamily: \"monospace\",\n                      children: event.source_ip\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 321,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      noWrap: true,\n                      children: event.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: event.is_resolved ? 'Resolved' : 'Open',\n                      color: event.is_resolved ? 'success' : 'warning',\n                      size: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: new Date(event.created_at).toLocaleString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 338,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 25\n                  }, this)]\n                }, event.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            textAlign: \"center\",\n            py: 4,\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              sx: {\n                fontSize: 64,\n                color: 'success.main',\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"No Security Events\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Your system is secure with no recent security events\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(SecurityPage, \"B80uoN3FHEkBSmOLkIhpR00wsf4=\", false, function () {\n  return [useQuery, useQuery];\n});\n_c = SecurityPage;\nexport default SecurityPage;\nvar _c;\n$RefreshReg$(_c, \"SecurityPage\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "<PERSON><PERSON>", "Security", "Warning", "CheckCircle", "Error", "<PERSON><PERSON><PERSON>", "useQuery", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SecurityPage", "_s", "_securityDashboard$lo", "_securityDashboard$lo2", "_securityDashboard$lo3", "data", "securityDashboard", "isLoading", "error", "dashboardError", "getSecurityDashboard", "retry", "refetchOnWindowFocus", "securityEvents", "eventsError", "getSecurityEvents", "page", "getRiskLevelColor", "level", "getRiskLevelIcon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "name", "content", "severity", "sx", "mb", "variant", "component", "gutterBottom", "security_events", "unresolved", "container", "spacing", "item", "xs", "sm", "md", "display", "alignItems", "justifyContent", "total_events", "fontSize", "last_24h", "critical", "textAlign", "login_attempts", "successful_last_24h", "failed_last_24h", "suspicious_last_24h", "mt", "threat_sources", "length", "slice", "map", "source", "index", "py", "borderBottom", "borderColor", "fontFamily", "source_ip", "label", "count", "size", "results", "event", "gap", "risk_level", "event_type", "replace", "noWrap", "description", "is_resolved", "Date", "created_at", "toLocaleString", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/pages/Security/SecurityPage.tsx"], "sourcesContent": ["// TrustVault - Security Page\n\nimport React from 'react';\nimport {\n  Box,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  Alert,\n} from '@mui/material';\nimport {\n  Security,\n  Warning,\n  CheckCircle,\n  Error,\n} from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useQuery } from 'react-query';\n\n// Services\nimport apiService from '../../services/api';\n\nconst SecurityPage: React.FC = () => {\n  const { data: securityDashboard, isLoading, error: dashboardError } = useQuery(\n    'security-dashboard',\n    apiService.getSecurityDashboard,\n    {\n      retry: 1,\n      refetchOnWindowFocus: false,\n    }\n  );\n\n  const { data: securityEvents, error: eventsError } = useQuery(\n    'security-events',\n    () => apiService.getSecurityEvents({ page: 1 }),\n    {\n      retry: 1,\n      refetchOnWindowFocus: false,\n    }\n  );\n\n  const getRiskLevelColor = (level: string) => {\n    switch (level) {\n      case 'CRITICAL':\n        return 'error';\n      case 'HIGH':\n        return 'warning';\n      case 'MEDIUM':\n        return 'info';\n      case 'LOW':\n        return 'success';\n      default:\n        return 'default';\n    }\n  };\n\n  const getRiskLevelIcon = (level: string) => {\n    switch (level) {\n      case 'CRITICAL':\n        return <Error color=\"error\" />;\n      case 'HIGH':\n        return <Warning color=\"warning\" />;\n      case 'MEDIUM':\n        return <Warning color=\"info\" />;\n      case 'LOW':\n        return <CheckCircle color=\"success\" />;\n      default:\n        return <Security />;\n    }\n  };\n\n  // Gestion des erreurs\n  if (dashboardError || eventsError) {\n    return (\n      <>\n        <Helmet>\n          <title>Security - TrustVault</title>\n          <meta name=\"description\" content=\"Security monitoring and events\" />\n        </Helmet>\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            Unable to load security data. Please check your connection and try again.\n          </Alert>\n        </Box>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <Helmet>\n        <title>Security - TrustVault</title>\n        <meta name=\"description\" content=\"Security monitoring and events\" />\n      </Helmet>\n\n      <Box>\n        {/* Header */}\n        <Box mb={4}>\n          <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n            Security Center\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Monitor security events and system status\n          </Typography>\n        </Box>\n\n        {/* Security Status Alert */}\n        {securityDashboard && securityDashboard.security_events && securityDashboard.security_events.unresolved > 0 && (\n          <Alert severity=\"warning\" sx={{ mb: 3 }}>\n            You have {securityDashboard.security_events.unresolved} unresolved security events that require attention.\n          </Alert>\n        )}\n\n        {/* Security Metrics */}\n        <Grid container spacing={3} mb={4}>\n          <Grid item xs={12} sm={6} md={3}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                  <Box>\n                    <Typography color=\"text.secondary\" gutterBottom>\n                      Total Events\n                    </Typography>\n                    <Typography variant=\"h4\">\n                      {securityDashboard?.security_events.total_events || 0}\n                    </Typography>\n                  </Box>\n                  <Security color=\"primary\" sx={{ fontSize: 40 }} />\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                  <Box>\n                    <Typography color=\"text.secondary\" gutterBottom>\n                      Last 24h\n                    </Typography>\n                    <Typography variant=\"h4\">\n                      {securityDashboard?.security_events.last_24h || 0}\n                    </Typography>\n                  </Box>\n                  <Warning color=\"warning\" sx={{ fontSize: 40 }} />\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                  <Box>\n                    <Typography color=\"text.secondary\" gutterBottom>\n                      Critical Events\n                    </Typography>\n                    <Typography variant=\"h4\" color=\"error\">\n                      {securityDashboard?.security_events.critical || 0}\n                    </Typography>\n                  </Box>\n                  <Error color=\"error\" sx={{ fontSize: 40 }} />\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                  <Box>\n                    <Typography color=\"text.secondary\" gutterBottom>\n                      Unresolved\n                    </Typography>\n                    <Typography variant=\"h4\" color=\"warning\">\n                      {securityDashboard?.security_events.unresolved || 0}\n                    </Typography>\n                  </Box>\n                  <Warning color=\"warning\" sx={{ fontSize: 40 }} />\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n\n        {/* Login Activity */}\n        <Grid container spacing={3} mb={4}>\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Typography variant=\"h6\" gutterBottom>\n                  Login Activity (24h)\n                </Typography>\n                \n                <Grid container spacing={2}>\n                  <Grid item xs={6}>\n                    <Box textAlign=\"center\">\n                      <Typography variant=\"h3\" color=\"success.main\">\n                        {securityDashboard?.login_attempts.successful_last_24h || 0}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Successful\n                      </Typography>\n                    </Box>\n                  </Grid>\n                  \n                  <Grid item xs={6}>\n                    <Box textAlign=\"center\">\n                      <Typography variant=\"h3\" color=\"error.main\">\n                        {securityDashboard?.login_attempts.failed_last_24h || 0}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Failed\n                      </Typography>\n                    </Box>\n                  </Grid>\n                </Grid>\n\n                {(securityDashboard?.login_attempts?.suspicious_last_24h ?? 0) > 0 && (\n                  <Alert severity=\"warning\" sx={{ mt: 2 }}>\n                    {securityDashboard?.login_attempts?.suspicious_last_24h} suspicious login attempts detected\n                  </Alert>\n                )}\n              </CardContent>\n            </Card>\n          </Grid>\n\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Typography variant=\"h6\" gutterBottom>\n                  Top Threat Sources\n                </Typography>\n                \n                {securityDashboard?.threat_sources && securityDashboard.threat_sources.length > 0 ? (\n                  <Box>\n                    {securityDashboard.threat_sources.slice(0, 5).map((source, index) => (\n                      <Box\n                        key={source.source_ip}\n                        display=\"flex\"\n                        justifyContent=\"space-between\"\n                        alignItems=\"center\"\n                        py={1}\n                        borderBottom={index < 4 ? 1 : 0}\n                        borderColor=\"divider\"\n                      >\n                        <Typography variant=\"body2\" fontFamily=\"monospace\">\n                          {source.source_ip}\n                        </Typography>\n                        <Chip\n                          label={`${source.count} events`}\n                          size=\"small\"\n                          color=\"warning\"\n                        />\n                      </Box>\n                    ))}\n                  </Box>\n                ) : (\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    No threat sources detected\n                  </Typography>\n                )}\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n\n        {/* Recent Security Events */}\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              Recent Security Events\n            </Typography>\n\n            {isLoading ? (\n              <Typography>Loading security events...</Typography>\n            ) : securityEvents && securityEvents.results.length > 0 ? (\n              <TableContainer component={Paper} variant=\"outlined\">\n                <Table>\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Event Type</TableCell>\n                      <TableCell>Risk Level</TableCell>\n                      <TableCell>Source IP</TableCell>\n                      <TableCell>Description</TableCell>\n                      <TableCell>Status</TableCell>\n                      <TableCell>Date</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {securityEvents.results.slice(0, 10).map((event) => (\n                      <TableRow key={event.id}>\n                        <TableCell>\n                          <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                            {getRiskLevelIcon(event.risk_level)}\n                            <Typography variant=\"body2\">\n                              {event.event_type.replace('_', ' ')}\n                            </Typography>\n                          </Box>\n                        </TableCell>\n                        <TableCell>\n                          <Chip\n                            label={event.risk_level}\n                            color={getRiskLevelColor(event.risk_level)}\n                            size=\"small\"\n                          />\n                        </TableCell>\n                        <TableCell>\n                          <Typography variant=\"body2\" fontFamily=\"monospace\">\n                            {event.source_ip}\n                          </Typography>\n                        </TableCell>\n                        <TableCell>\n                          <Typography variant=\"body2\" noWrap>\n                            {event.description}\n                          </Typography>\n                        </TableCell>\n                        <TableCell>\n                          <Chip\n                            label={event.is_resolved ? 'Resolved' : 'Open'}\n                            color={event.is_resolved ? 'success' : 'warning'}\n                            size=\"small\"\n                          />\n                        </TableCell>\n                        <TableCell>\n                          <Typography variant=\"body2\">\n                            {new Date(event.created_at).toLocaleString()}\n                          </Typography>\n                        </TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            ) : (\n              <Box textAlign=\"center\" py={4}>\n                <CheckCircle sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />\n                <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                  No Security Events\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Your system is secure with no recent security events\n                </Typography>\n              </Box>\n            )}\n          </CardContent>\n        </Card>\n      </Box>\n    </>\n  );\n};\n\nexport default SecurityPage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,KAAK,QACA,eAAe;AACtB,SACEC,QAAQ,EACRC,OAAO,EACPC,WAAW,EACXC,KAAK,QACA,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,QAAQ,QAAQ,aAAa;;AAEtC;AACA,OAAOC,UAAU,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACnC,MAAM;IAAEC,IAAI,EAAEC,iBAAiB;IAAEC,SAAS;IAAEC,KAAK,EAAEC;EAAe,CAAC,GAAGf,QAAQ,CAC5E,oBAAoB,EACpBC,UAAU,CAACe,oBAAoB,EAC/B;IACEC,KAAK,EAAE,CAAC;IACRC,oBAAoB,EAAE;EACxB,CACF,CAAC;EAED,MAAM;IAAEP,IAAI,EAAEQ,cAAc;IAAEL,KAAK,EAAEM;EAAY,CAAC,GAAGpB,QAAQ,CAC3D,iBAAiB,EACjB,MAAMC,UAAU,CAACoB,iBAAiB,CAAC;IAAEC,IAAI,EAAE;EAAE,CAAC,CAAC,EAC/C;IACEL,KAAK,EAAE,CAAC;IACRC,oBAAoB,EAAE;EACxB,CACF,CAAC;EAED,MAAMK,iBAAiB,GAAIC,KAAa,IAAK;IAC3C,QAAQA,KAAK;MACX,KAAK,UAAU;QACb,OAAO,OAAO;MAChB,KAAK,MAAM;QACT,OAAO,SAAS;MAClB,KAAK,QAAQ;QACX,OAAO,MAAM;MACf,KAAK,KAAK;QACR,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAID,KAAa,IAAK;IAC1C,QAAQA,KAAK;MACX,KAAK,UAAU;QACb,oBAAOrB,OAAA,CAACL,KAAK;UAAC4B,KAAK,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChC,KAAK,MAAM;QACT,oBAAO3B,OAAA,CAACP,OAAO;UAAC8B,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpC,KAAK,QAAQ;QACX,oBAAO3B,OAAA,CAACP,OAAO;UAAC8B,KAAK,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjC,KAAK,KAAK;QACR,oBAAO3B,OAAA,CAACN,WAAW;UAAC6B,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxC;QACE,oBAAO3B,OAAA,CAACR,QAAQ;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACvB;EACF,CAAC;;EAED;EACA,IAAIf,cAAc,IAAIK,WAAW,EAAE;IACjC,oBACEjB,OAAA,CAAAE,SAAA;MAAA0B,QAAA,gBACE5B,OAAA,CAACJ,MAAM;QAAAgC,QAAA,gBACL5B,OAAA;UAAA4B,QAAA,EAAO;QAAqB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpC3B,OAAA;UAAM6B,IAAI,EAAC,aAAa;UAACC,OAAO,EAAC;QAAgC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACT3B,OAAA,CAACtB,GAAG;QAAAkD,QAAA,eACF5B,OAAA,CAACT,KAAK;UAACwC,QAAQ,EAAC,OAAO;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAL,QAAA,EAAC;QAEvC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA,eACN,CAAC;EAEP;EAEA,oBACE3B,OAAA,CAAAE,SAAA;IAAA0B,QAAA,gBACE5B,OAAA,CAACJ,MAAM;MAAAgC,QAAA,gBACL5B,OAAA;QAAA4B,QAAA,EAAO;MAAqB;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpC3B,OAAA;QAAM6B,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAAgC;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,eAET3B,OAAA,CAACtB,GAAG;MAAAkD,QAAA,gBAEF5B,OAAA,CAACtB,GAAG;QAACuD,EAAE,EAAE,CAAE;QAAAL,QAAA,gBACT5B,OAAA,CAACrB,UAAU;UAACuD,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAACC,YAAY;UAAAR,QAAA,EAAC;QAErD;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3B,OAAA,CAACrB,UAAU;UAACuD,OAAO,EAAC,OAAO;UAACX,KAAK,EAAC,gBAAgB;UAAAK,QAAA,EAAC;QAEnD;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EAGLlB,iBAAiB,IAAIA,iBAAiB,CAAC4B,eAAe,IAAI5B,iBAAiB,CAAC4B,eAAe,CAACC,UAAU,GAAG,CAAC,iBACzGtC,OAAA,CAACT,KAAK;QAACwC,QAAQ,EAAC,SAAS;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,GAAC,WAC9B,EAACnB,iBAAiB,CAAC4B,eAAe,CAACC,UAAU,EAAC,qDACzD;MAAA;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,eAGD3B,OAAA,CAACpB,IAAI;QAAC2D,SAAS;QAACC,OAAO,EAAE,CAAE;QAACP,EAAE,EAAE,CAAE;QAAAL,QAAA,gBAChC5B,OAAA,CAACpB,IAAI;UAAC6D,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eAC9B5B,OAAA,CAACnB,IAAI;YAAA+C,QAAA,eACH5B,OAAA,CAAClB,WAAW;cAAA8C,QAAA,eACV5B,OAAA,CAACtB,GAAG;gBAACmE,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,cAAc,EAAC,eAAe;gBAAAnB,QAAA,gBACpE5B,OAAA,CAACtB,GAAG;kBAAAkD,QAAA,gBACF5B,OAAA,CAACrB,UAAU;oBAAC4C,KAAK,EAAC,gBAAgB;oBAACa,YAAY;oBAAAR,QAAA,EAAC;kBAEhD;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb3B,OAAA,CAACrB,UAAU;oBAACuD,OAAO,EAAC,IAAI;oBAAAN,QAAA,EACrB,CAAAnB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE4B,eAAe,CAACW,YAAY,KAAI;kBAAC;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN3B,OAAA,CAACR,QAAQ;kBAAC+B,KAAK,EAAC,SAAS;kBAACS,EAAE,EAAE;oBAAEiB,QAAQ,EAAE;kBAAG;gBAAE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEP3B,OAAA,CAACpB,IAAI;UAAC6D,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eAC9B5B,OAAA,CAACnB,IAAI;YAAA+C,QAAA,eACH5B,OAAA,CAAClB,WAAW;cAAA8C,QAAA,eACV5B,OAAA,CAACtB,GAAG;gBAACmE,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,cAAc,EAAC,eAAe;gBAAAnB,QAAA,gBACpE5B,OAAA,CAACtB,GAAG;kBAAAkD,QAAA,gBACF5B,OAAA,CAACrB,UAAU;oBAAC4C,KAAK,EAAC,gBAAgB;oBAACa,YAAY;oBAAAR,QAAA,EAAC;kBAEhD;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb3B,OAAA,CAACrB,UAAU;oBAACuD,OAAO,EAAC,IAAI;oBAAAN,QAAA,EACrB,CAAAnB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE4B,eAAe,CAACa,QAAQ,KAAI;kBAAC;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN3B,OAAA,CAACP,OAAO;kBAAC8B,KAAK,EAAC,SAAS;kBAACS,EAAE,EAAE;oBAAEiB,QAAQ,EAAE;kBAAG;gBAAE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEP3B,OAAA,CAACpB,IAAI;UAAC6D,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eAC9B5B,OAAA,CAACnB,IAAI;YAAA+C,QAAA,eACH5B,OAAA,CAAClB,WAAW;cAAA8C,QAAA,eACV5B,OAAA,CAACtB,GAAG;gBAACmE,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,cAAc,EAAC,eAAe;gBAAAnB,QAAA,gBACpE5B,OAAA,CAACtB,GAAG;kBAAAkD,QAAA,gBACF5B,OAAA,CAACrB,UAAU;oBAAC4C,KAAK,EAAC,gBAAgB;oBAACa,YAAY;oBAAAR,QAAA,EAAC;kBAEhD;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb3B,OAAA,CAACrB,UAAU;oBAACuD,OAAO,EAAC,IAAI;oBAACX,KAAK,EAAC,OAAO;oBAAAK,QAAA,EACnC,CAAAnB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE4B,eAAe,CAACc,QAAQ,KAAI;kBAAC;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN3B,OAAA,CAACL,KAAK;kBAAC4B,KAAK,EAAC,OAAO;kBAACS,EAAE,EAAE;oBAAEiB,QAAQ,EAAE;kBAAG;gBAAE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEP3B,OAAA,CAACpB,IAAI;UAAC6D,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eAC9B5B,OAAA,CAACnB,IAAI;YAAA+C,QAAA,eACH5B,OAAA,CAAClB,WAAW;cAAA8C,QAAA,eACV5B,OAAA,CAACtB,GAAG;gBAACmE,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,cAAc,EAAC,eAAe;gBAAAnB,QAAA,gBACpE5B,OAAA,CAACtB,GAAG;kBAAAkD,QAAA,gBACF5B,OAAA,CAACrB,UAAU;oBAAC4C,KAAK,EAAC,gBAAgB;oBAACa,YAAY;oBAAAR,QAAA,EAAC;kBAEhD;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb3B,OAAA,CAACrB,UAAU;oBAACuD,OAAO,EAAC,IAAI;oBAACX,KAAK,EAAC,SAAS;oBAAAK,QAAA,EACrC,CAAAnB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE4B,eAAe,CAACC,UAAU,KAAI;kBAAC;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN3B,OAAA,CAACP,OAAO;kBAAC8B,KAAK,EAAC,SAAS;kBAACS,EAAE,EAAE;oBAAEiB,QAAQ,EAAE;kBAAG;gBAAE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGP3B,OAAA,CAACpB,IAAI;QAAC2D,SAAS;QAACC,OAAO,EAAE,CAAE;QAACP,EAAE,EAAE,CAAE;QAAAL,QAAA,gBAChC5B,OAAA,CAACpB,IAAI;UAAC6D,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvB5B,OAAA,CAACnB,IAAI;YAAA+C,QAAA,eACH5B,OAAA,CAAClB,WAAW;cAAA8C,QAAA,gBACV5B,OAAA,CAACrB,UAAU;gBAACuD,OAAO,EAAC,IAAI;gBAACE,YAAY;gBAAAR,QAAA,EAAC;cAEtC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEb3B,OAAA,CAACpB,IAAI;gBAAC2D,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAZ,QAAA,gBACzB5B,OAAA,CAACpB,IAAI;kBAAC6D,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACf5B,OAAA,CAACtB,GAAG;oBAAC0E,SAAS,EAAC,QAAQ;oBAAAxB,QAAA,gBACrB5B,OAAA,CAACrB,UAAU;sBAACuD,OAAO,EAAC,IAAI;sBAACX,KAAK,EAAC,cAAc;sBAAAK,QAAA,EAC1C,CAAAnB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE4C,cAAc,CAACC,mBAAmB,KAAI;oBAAC;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC,eACb3B,OAAA,CAACrB,UAAU;sBAACuD,OAAO,EAAC,OAAO;sBAACX,KAAK,EAAC,gBAAgB;sBAAAK,QAAA,EAAC;oBAEnD;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eAEP3B,OAAA,CAACpB,IAAI;kBAAC6D,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACf5B,OAAA,CAACtB,GAAG;oBAAC0E,SAAS,EAAC,QAAQ;oBAAAxB,QAAA,gBACrB5B,OAAA,CAACrB,UAAU;sBAACuD,OAAO,EAAC,IAAI;sBAACX,KAAK,EAAC,YAAY;sBAAAK,QAAA,EACxC,CAAAnB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE4C,cAAc,CAACE,eAAe,KAAI;oBAAC;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACb3B,OAAA,CAACrB,UAAU;sBAACuD,OAAO,EAAC,OAAO;sBAACX,KAAK,EAAC,gBAAgB;sBAAAK,QAAA,EAAC;oBAEnD;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEN,EAAAtB,qBAAA,GAACI,iBAAiB,aAAjBA,iBAAiB,wBAAAH,sBAAA,GAAjBG,iBAAiB,CAAE4C,cAAc,cAAA/C,sBAAA,uBAAjCA,sBAAA,CAAmCkD,mBAAmB,cAAAnD,qBAAA,cAAAA,qBAAA,GAAI,CAAC,IAAI,CAAC,iBAChEL,OAAA,CAACT,KAAK;gBAACwC,QAAQ,EAAC,SAAS;gBAACC,EAAE,EAAE;kBAAEyB,EAAE,EAAE;gBAAE,CAAE;gBAAA7B,QAAA,GACrCnB,iBAAiB,aAAjBA,iBAAiB,wBAAAF,sBAAA,GAAjBE,iBAAiB,CAAE4C,cAAc,cAAA9C,sBAAA,uBAAjCA,sBAAA,CAAmCiD,mBAAmB,EAAC,qCAC1D;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEP3B,OAAA,CAACpB,IAAI;UAAC6D,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvB5B,OAAA,CAACnB,IAAI;YAAA+C,QAAA,eACH5B,OAAA,CAAClB,WAAW;cAAA8C,QAAA,gBACV5B,OAAA,CAACrB,UAAU;gBAACuD,OAAO,EAAC,IAAI;gBAACE,YAAY;gBAAAR,QAAA,EAAC;cAEtC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAEZlB,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEiD,cAAc,IAAIjD,iBAAiB,CAACiD,cAAc,CAACC,MAAM,GAAG,CAAC,gBAC/E3D,OAAA,CAACtB,GAAG;gBAAAkD,QAAA,EACDnB,iBAAiB,CAACiD,cAAc,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC9D/D,OAAA,CAACtB,GAAG;kBAEFmE,OAAO,EAAC,MAAM;kBACdE,cAAc,EAAC,eAAe;kBAC9BD,UAAU,EAAC,QAAQ;kBACnBkB,EAAE,EAAE,CAAE;kBACNC,YAAY,EAAEF,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAE;kBAChCG,WAAW,EAAC,SAAS;kBAAAtC,QAAA,gBAErB5B,OAAA,CAACrB,UAAU;oBAACuD,OAAO,EAAC,OAAO;oBAACiC,UAAU,EAAC,WAAW;oBAAAvC,QAAA,EAC/CkC,MAAM,CAACM;kBAAS;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACb3B,OAAA,CAACV,IAAI;oBACH+E,KAAK,EAAE,GAAGP,MAAM,CAACQ,KAAK,SAAU;oBAChCC,IAAI,EAAC,OAAO;oBACZhD,KAAK,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA,GAfGmC,MAAM,CAACM,SAAS;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgBlB,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAEN3B,OAAA,CAACrB,UAAU;gBAACuD,OAAO,EAAC,OAAO;gBAACX,KAAK,EAAC,gBAAgB;gBAAAK,QAAA,EAAC;cAEnD;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGP3B,OAAA,CAACnB,IAAI;QAAA+C,QAAA,eACH5B,OAAA,CAAClB,WAAW;UAAA8C,QAAA,gBACV5B,OAAA,CAACrB,UAAU;YAACuD,OAAO,EAAC,IAAI;YAACE,YAAY;YAAAR,QAAA,EAAC;UAEtC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAEZjB,SAAS,gBACRV,OAAA,CAACrB,UAAU;YAAAiD,QAAA,EAAC;UAA0B;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,GACjDX,cAAc,IAAIA,cAAc,CAACwD,OAAO,CAACb,MAAM,GAAG,CAAC,gBACrD3D,OAAA,CAACd,cAAc;YAACiD,SAAS,EAAE9C,KAAM;YAAC6C,OAAO,EAAC,UAAU;YAAAN,QAAA,eAClD5B,OAAA,CAACjB,KAAK;cAAA6C,QAAA,gBACJ5B,OAAA,CAACb,SAAS;gBAAAyC,QAAA,eACR5B,OAAA,CAACZ,QAAQ;kBAAAwC,QAAA,gBACP5B,OAAA,CAACf,SAAS;oBAAA2C,QAAA,EAAC;kBAAU;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACjC3B,OAAA,CAACf,SAAS;oBAAA2C,QAAA,EAAC;kBAAU;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACjC3B,OAAA,CAACf,SAAS;oBAAA2C,QAAA,EAAC;kBAAS;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAChC3B,OAAA,CAACf,SAAS;oBAAA2C,QAAA,EAAC;kBAAW;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAClC3B,OAAA,CAACf,SAAS;oBAAA2C,QAAA,EAAC;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC7B3B,OAAA,CAACf,SAAS;oBAAA2C,QAAA,EAAC;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZ3B,OAAA,CAAChB,SAAS;gBAAA4C,QAAA,EACPZ,cAAc,CAACwD,OAAO,CAACZ,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,GAAG,CAAEY,KAAK,iBAC7CzE,OAAA,CAACZ,QAAQ;kBAAAwC,QAAA,gBACP5B,OAAA,CAACf,SAAS;oBAAA2C,QAAA,eACR5B,OAAA,CAACtB,GAAG;sBAACmE,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAAC4B,GAAG,EAAE,CAAE;sBAAA9C,QAAA,GAC5CN,gBAAgB,CAACmD,KAAK,CAACE,UAAU,CAAC,eACnC3E,OAAA,CAACrB,UAAU;wBAACuD,OAAO,EAAC,OAAO;wBAAAN,QAAA,EACxB6C,KAAK,CAACG,UAAU,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG;sBAAC;wBAAArD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZ3B,OAAA,CAACf,SAAS;oBAAA2C,QAAA,eACR5B,OAAA,CAACV,IAAI;sBACH+E,KAAK,EAAEI,KAAK,CAACE,UAAW;sBACxBpD,KAAK,EAAEH,iBAAiB,CAACqD,KAAK,CAACE,UAAU,CAAE;sBAC3CJ,IAAI,EAAC;oBAAO;sBAAA/C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZ3B,OAAA,CAACf,SAAS;oBAAA2C,QAAA,eACR5B,OAAA,CAACrB,UAAU;sBAACuD,OAAO,EAAC,OAAO;sBAACiC,UAAU,EAAC,WAAW;sBAAAvC,QAAA,EAC/C6C,KAAK,CAACL;oBAAS;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZ3B,OAAA,CAACf,SAAS;oBAAA2C,QAAA,eACR5B,OAAA,CAACrB,UAAU;sBAACuD,OAAO,EAAC,OAAO;sBAAC4C,MAAM;sBAAAlD,QAAA,EAC/B6C,KAAK,CAACM;oBAAW;sBAAAvD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZ3B,OAAA,CAACf,SAAS;oBAAA2C,QAAA,eACR5B,OAAA,CAACV,IAAI;sBACH+E,KAAK,EAAEI,KAAK,CAACO,WAAW,GAAG,UAAU,GAAG,MAAO;sBAC/CzD,KAAK,EAAEkD,KAAK,CAACO,WAAW,GAAG,SAAS,GAAG,SAAU;sBACjDT,IAAI,EAAC;oBAAO;sBAAA/C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZ3B,OAAA,CAACf,SAAS;oBAAA2C,QAAA,eACR5B,OAAA,CAACrB,UAAU;sBAACuD,OAAO,EAAC,OAAO;sBAAAN,QAAA,EACxB,IAAIqD,IAAI,CAACR,KAAK,CAACS,UAAU,CAAC,CAACC,cAAc,CAAC;oBAAC;sBAAA3D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GArCC8C,KAAK,CAACW,EAAE;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsCb,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,gBAEjB3B,OAAA,CAACtB,GAAG;YAAC0E,SAAS,EAAC,QAAQ;YAACY,EAAE,EAAE,CAAE;YAAApC,QAAA,gBAC5B5B,OAAA,CAACN,WAAW;cAACsC,EAAE,EAAE;gBAAEiB,QAAQ,EAAE,EAAE;gBAAE1B,KAAK,EAAE,cAAc;gBAAEU,EAAE,EAAE;cAAE;YAAE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnE3B,OAAA,CAACrB,UAAU;cAACuD,OAAO,EAAC,IAAI;cAACX,KAAK,EAAC,gBAAgB;cAACa,YAAY;cAAAR,QAAA,EAAC;YAE7D;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3B,OAAA,CAACrB,UAAU;cAACuD,OAAO,EAAC,OAAO;cAACX,KAAK,EAAC,gBAAgB;cAAAK,QAAA,EAAC;YAEnD;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACvB,EAAA,CA3UID,YAAsB;EAAA,QAC4CN,QAAQ,EASzBA,QAAQ;AAAA;AAAAwF,EAAA,GAVzDlF,YAAsB;AA6U5B,eAAeA,YAAY;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}