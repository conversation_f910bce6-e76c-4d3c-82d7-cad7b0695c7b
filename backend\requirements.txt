# TrustVault - Django Backend Requirements

# Django Core
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1
django-environ==0.11.2

# Authentication & Security
djangorestframework-simplejwt==5.3.0
django-otp==1.2.2
qrcode==7.4.2
cryptography==41.0.7
bcrypt==4.1.2
django-ratelimit==4.1.0
django-axes==6.1.1

# Database
psycopg2-binary==2.9.7
redis==5.0.1
django-redis==5.4.0

# API Documentation
drf-spectacular==0.26.5
drf-spectacular-sidecar==2023.10.1

# Monitoring & Logging
django-prometheus==2.3.1
structlog==23.2.0
django-structlog==6.0.0
sentry-sdk==1.38.0

# Security Headers
django-security==0.17.0
django-csp==3.7

# Validation & Serialization
marshmallow==3.20.1
django-filter==23.4

# Task Queue
celery==5.3.4
django-celery-beat==2.5.0
django-celery-results==2.5.0

# File Handling
Pillow==10.1.0
python-magic==0.4.27

# HTTP Client
requests==2.31.0
httpx==0.25.2

# Utilities
python-decouple==3.8
python-dateutil==2.8.2
pytz==2023.3
uuid==1.30

# Development & Testing
pytest==7.4.3
pytest-django==4.7.0
pytest-cov==4.1.0
factory-boy==3.3.0
faker==20.1.0

# WSGI Server
gunicorn==21.2.0
whitenoise==6.6.0

# Vault Integration
hvac==2.0.0

# Encryption
pycryptodome==3.19.0

# Compliance
django-audit-log==2.1.0

# Security Advanced Dependencies
pyotp==2.9.0
user-agents==2.2.0
geoip2==4.7.0
aiohttp==3.9.1
asyncio==3.4.3

# Network Security
python-nmap==0.7.1
scapy==2.5.0

# Threat Intelligence
yara-python==4.3.1
requests-oauthlib==1.3.1

# Additional Cryptography
pynacl==1.5.0
jwcrypto==1.5.0

# Database Extensions
django-extensions==3.2.3
django-model-utils==4.3.1

# JSON Field Support (for older Django versions)
django-jsonfield==3.1.0

# Additional Security
django-guardian==2.4.0
django-two-factor-auth==1.15.0

# Performance Monitoring
django-silk==5.0.4
django-prometheus==2.3.1

# Advanced Logging
python-json-logger==2.0.7
colorlog==6.8.0
