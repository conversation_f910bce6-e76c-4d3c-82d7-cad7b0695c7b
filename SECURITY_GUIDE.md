# 🛡️ TrustVault Security Guide

This comprehensive guide covers all security features, best practices, and configurations for TrustVault.

## 🔐 Security Architecture Overview

TrustVault implements a multi-layered security approach:

1. **Authentication Layer**: JWT tokens with MFA support
2. **Authorization Layer**: Role-based access control (RBAC)
3. **Transport Layer**: TLS/SSL encryption
4. **Application Layer**: Input validation, CSRF protection
5. **Data Layer**: Encryption at rest, secure database connections
6. **Infrastructure Layer**: Container security, network isolation

## 🔑 Authentication & Authorization

### JWT Token Security

**Features:**
- Short-lived access tokens (15 minutes default)
- Long-lived refresh tokens (7 days default)
- Token rotation on refresh
- Secure token storage recommendations

**Configuration:**
```python
# backend/trustvault/settings.py
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=15),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': os.getenv('JWT_SECRET_KEY'),
}
```

### Multi-Factor Authentication (MFA)

**Implementation:**
- TOTP (Time-based One-Time Password) using RFC 6238
- QR code generation for easy setup
- Backup codes for account recovery
- MFA enforcement policies

**Setup Process:**
1. User enables MFA in security settings
2. System generates TOTP secret and QR code
3. User scans QR code with authenticator app
4. User verifies setup with TOTP token
5. System enables MFA for the account

**API Endpoints:**
```http
POST /api/v1/auth/mfa/setup/     # Initialize MFA setup
POST /api/v1/auth/mfa/verify/    # Verify and enable MFA
POST /api/v1/auth/mfa/disable/   # Disable MFA
GET  /api/v1/auth/mfa/qr-code/   # Get QR code image
```

### Session Management

**Features:**
- Session tracking and monitoring
- Device fingerprinting
- Concurrent session limits
- Session revocation capabilities

**Session Security:**
```python
# Session configuration
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Strict'
SESSION_EXPIRE_AT_BROWSER_CLOSE = True
```

## 🔒 Data Protection

### Encryption at Rest

**Database Encryption:**
```sql
-- PostgreSQL encryption
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Encrypt sensitive fields
UPDATE users SET 
    encrypted_field = pgp_sym_encrypt(sensitive_data, 'encryption_key');
```

**File Encryption:**
```python
from cryptography.fernet import Fernet

# Generate encryption key
key = Fernet.generate_key()
cipher_suite = Fernet(key)

# Encrypt data
encrypted_data = cipher_suite.encrypt(data.encode())
```

### Encryption in Transit

**TLS/SSL Configuration:**
```nginx
# nginx SSL configuration
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;

# Security headers
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
add_header X-Frame-Options "DENY" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
```

## 🛡️ Application Security

### Input Validation & Sanitization

**Django Forms Validation:**
```python
from django import forms
from django.core.validators import RegexValidator

class SecureForm(forms.Form):
    # Email validation
    email = forms.EmailField(
        validators=[EmailValidator(message="Invalid email format")]
    )
    
    # Password strength validation
    password = forms.CharField(
        validators=[
            RegexValidator(
                regex=r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$',
                message="Password must contain at least 8 characters, including uppercase, lowercase, digit, and special character"
            )
        ]
    )
```

**Frontend Validation:**
```typescript
// Input sanitization
import DOMPurify from 'dompurify';

const sanitizeInput = (input: string): string => {
  return DOMPurify.sanitize(input);
};

// XSS prevention
const escapeHtml = (text: string): string => {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
};
```

### CSRF Protection

**Django CSRF Configuration:**
```python
# settings.py
CSRF_COOKIE_SECURE = True
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Strict'
CSRF_TRUSTED_ORIGINS = ['https://your-domain.com']

# Middleware
MIDDLEWARE = [
    'django.middleware.csrf.CsrfViewMiddleware',
    # ... other middleware
]
```

**Frontend CSRF Handling:**
```typescript
// Get CSRF token
const getCsrfToken = (): string => {
  const token = document.querySelector('[name=csrfmiddlewaretoken]') as HTMLInputElement;
  return token ? token.value : '';
};

// Include in API requests
axios.defaults.headers.common['X-CSRFToken'] = getCsrfToken();
```

### SQL Injection Prevention

**Django ORM (Recommended):**
```python
# Safe - uses parameterized queries
users = User.objects.filter(email=user_email)

# Safe - using Q objects
from django.db.models import Q
users = User.objects.filter(Q(email=user_email) & Q(is_active=True))
```

**Raw Queries (When Necessary):**
```python
# Safe - using parameters
cursor.execute(
    "SELECT * FROM users WHERE email = %s AND is_active = %s",
    [user_email, True]
)
```

## 🔍 Security Monitoring

### Audit Logging

**Comprehensive Logging:**
```python
# Audit log model
class AuditLog(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    action = models.CharField(max_length=100)
    resource_type = models.CharField(max_length=50)
    resource_id = models.CharField(max_length=100)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    severity = models.CharField(max_length=20, default='INFO')
    details = models.JSONField(default=dict)
```

**Logged Events:**
- User authentication (login, logout, failed attempts)
- Password changes and resets
- MFA setup, verification, and disabling
- Portfolio operations (create, update, delete)
- Transaction operations
- Security events and alerts
- Administrative actions

### Security Events Detection

**Suspicious Activity Detection:**
```python
class SecurityEventDetector:
    def detect_brute_force(self, user_id, ip_address):
        """Detect brute force login attempts."""
        recent_failures = AuditLog.objects.filter(
            user_id=user_id,
            action='LOGIN_FAILED',
            ip_address=ip_address,
            timestamp__gte=timezone.now() - timedelta(minutes=15)
        ).count()
        
        if recent_failures >= 5:
            self.create_security_event(
                event_type='BRUTE_FORCE_ATTEMPT',
                risk_level='HIGH',
                details={'attempts': recent_failures}
            )
    
    def detect_unusual_location(self, user_id, ip_address):
        """Detect login from unusual location."""
        # Implementation for geolocation checking
        pass
```

### Rate Limiting

**Django Rate Limiting:**
```python
from django_ratelimit.decorators import ratelimit

@ratelimit(key='ip', rate='5/m', method='POST')
def login_view(request):
    # Login logic
    pass

@ratelimit(key='user', rate='100/h')
def api_view(request):
    # API logic
    pass
```

**Nginx Rate Limiting:**
```nginx
# Rate limiting configuration
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;

location /api/v1/auth/login/ {
    limit_req zone=login burst=5 nodelay;
    proxy_pass http://backend;
}

location /api/v1/ {
    limit_req zone=api burst=20 nodelay;
    proxy_pass http://backend;
}
```

## 🔐 Infrastructure Security

### Container Security

**Dockerfile Security Best Practices:**
```dockerfile
# Use specific version tags
FROM python:3.11-slim

# Create non-root user
RUN groupadd -r trustvault && useradd -r -g trustvault trustvault

# Remove unnecessary packages
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set secure permissions
COPY --chown=trustvault:trustvault . /app
USER trustvault

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health/ || exit 1
```

**Docker Compose Security:**
```yaml
services:
  backend:
    image: trustvault-backend
    user: "1000:1000"
    read_only: true
    tmpfs:
      - /tmp
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    security_opt:
      - no-new-privileges:true
```

### Network Security

**Docker Network Isolation:**
```yaml
networks:
  frontend-network:
    driver: bridge
  backend-network:
    driver: bridge
    internal: true

services:
  frontend:
    networks:
      - frontend-network
  
  backend:
    networks:
      - frontend-network
      - backend-network
  
  database:
    networks:
      - backend-network
```

**Firewall Configuration:**
```bash
# UFW (Ubuntu Firewall) rules
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

## 🚨 Incident Response

### Security Incident Handling

**Incident Response Plan:**
1. **Detection**: Automated alerts and monitoring
2. **Assessment**: Evaluate severity and impact
3. **Containment**: Isolate affected systems
4. **Eradication**: Remove threats and vulnerabilities
5. **Recovery**: Restore normal operations
6. **Lessons Learned**: Document and improve

**Emergency Procedures:**
```bash
# Disable user account
python manage.py shell -c "
from django.contrib.auth import get_user_model;
User = get_user_model();
user = User.objects.get(email='<EMAIL>');
user.is_active = False;
user.save()
"

# Revoke all user sessions
python manage.py shell -c "
from apps.authentication.models import UserSession;
UserSession.objects.filter(user__email='<EMAIL>').update(is_active=False)
"

# Block IP address
sudo ufw insert 1 deny from <malicious-ip>
```

### Backup and Recovery

**Database Backup:**
```bash
# Automated backup script
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="trustvault"

# Create encrypted backup
pg_dump $DB_NAME | gpg --cipher-algo AES256 --compress-algo 1 \
  --symmetric --output "$BACKUP_DIR/backup_$DATE.sql.gpg"

# Upload to secure storage
aws s3 cp "$BACKUP_DIR/backup_$DATE.sql.gpg" \
  s3://trustvault-backups/ --server-side-encryption AES256
```

**Recovery Procedures:**
```bash
# Restore from backup
gpg --decrypt backup_20240101_120000.sql.gpg | psql trustvault

# Verify data integrity
python manage.py check --deploy
python manage.py test
```

## 📋 Security Checklist

### Pre-Production Security Checklist

- [ ] **Authentication**
  - [ ] Strong password policies enforced
  - [ ] MFA enabled for admin accounts
  - [ ] JWT tokens properly configured
  - [ ] Session management implemented

- [ ] **Authorization**
  - [ ] Role-based access control configured
  - [ ] Principle of least privilege applied
  - [ ] API permissions properly set

- [ ] **Data Protection**
  - [ ] Sensitive data encrypted at rest
  - [ ] TLS/SSL properly configured
  - [ ] Database connections encrypted
  - [ ] Backup encryption enabled

- [ ] **Application Security**
  - [ ] Input validation implemented
  - [ ] CSRF protection enabled
  - [ ] XSS prevention measures
  - [ ] SQL injection prevention

- [ ] **Infrastructure Security**
  - [ ] Container security hardened
  - [ ] Network segmentation implemented
  - [ ] Firewall rules configured
  - [ ] Security headers enabled

- [ ] **Monitoring & Logging**
  - [ ] Audit logging enabled
  - [ ] Security event detection
  - [ ] Rate limiting configured
  - [ ] Monitoring alerts set up

- [ ] **Incident Response**
  - [ ] Response plan documented
  - [ ] Emergency procedures tested
  - [ ] Backup and recovery tested
  - [ ] Contact information updated

### Regular Security Maintenance

**Weekly Tasks:**
- Review security logs and alerts
- Check for failed login attempts
- Monitor unusual activity patterns
- Update security patches

**Monthly Tasks:**
- Security vulnerability assessment
- Review user access permissions
- Test backup and recovery procedures
- Update security documentation

**Quarterly Tasks:**
- Penetration testing
- Security policy review
- Incident response plan testing
- Security training updates

---

**For implementation details, see the [Setup Guide](SETUP_GUIDE.md) and [Deployment Guide](DEPLOYMENT_GUIDE.md)**
