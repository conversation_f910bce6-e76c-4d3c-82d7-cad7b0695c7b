#!/usr/bin/env python3

"""
Script de test pour l'API de sécurité TrustVault
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:8000"

def test_api_endpoints():
    """Test des endpoints de l'API de sécurité"""
    print("🌐 Test des endpoints de l'API de sécurité...")
    
    endpoints_to_test = [
        "/api/v1/",
        "/api/v1/auth/",
        "/api/v1/security/",
        "/api/v1/core/",
        "/api/v1/portfolio/",
    ]
    
    results = []
    
    for endpoint in endpoints_to_test:
        try:
            url = f"{BASE_URL}{endpoint}"
            response = requests.get(url, timeout=5)
            
            result = {
                'endpoint': endpoint,
                'status_code': response.status_code,
                'accessible': response.status_code != 404,
                'response_time': response.elapsed.total_seconds()
            }
            
            if response.status_code == 200:
                try:
                    result['response_data'] = response.json()
                except:
                    result['response_data'] = response.text[:100]
            
            results.append(result)
            
            status_emoji = "✅" if result['accessible'] else "❌"
            print(f"  {status_emoji} {endpoint} - Status: {response.status_code} - Time: {result['response_time']:.3f}s")
            
        except requests.exceptions.RequestException as e:
            result = {
                'endpoint': endpoint,
                'status_code': None,
                'accessible': False,
                'error': str(e)
            }
            results.append(result)
            print(f"  ❌ {endpoint} - Error: {e}")
    
    return results

def test_security_headers():
    """Test des en-têtes de sécurité"""
    print("\n🛡️ Test des en-têtes de sécurité...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/", timeout=5)
        headers = response.headers
        
        security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': None,  # Should be present
            'Content-Security-Policy': None,    # Should be present
        }
        
        results = {}
        for header, expected_value in security_headers.items():
            if header in headers:
                actual_value = headers[header]
                if expected_value is None:
                    results[header] = {'present': True, 'value': actual_value, 'status': '✅'}
                elif actual_value == expected_value:
                    results[header] = {'present': True, 'value': actual_value, 'status': '✅'}
                else:
                    results[header] = {'present': True, 'value': actual_value, 'expected': expected_value, 'status': '⚠️'}
            else:
                results[header] = {'present': False, 'status': '❌'}
            
            print(f"  {results[header]['status']} {header}: {results[header].get('value', 'Missing')}")
        
        return results
        
    except Exception as e:
        print(f"  ❌ Erreur lors du test des en-têtes: {e}")
        return {}

def test_rate_limiting():
    """Test du rate limiting"""
    print("\n⏱️ Test du rate limiting...")
    
    try:
        # Faire plusieurs requêtes rapides pour tester le rate limiting
        responses = []
        for i in range(10):
            response = requests.get(f"{BASE_URL}/api/v1/", timeout=5)
            responses.append({
                'request_number': i + 1,
                'status_code': response.status_code,
                'headers': dict(response.headers)
            })
            time.sleep(0.1)  # Petite pause entre les requêtes
        
        # Analyser les réponses
        rate_limited = any(r['status_code'] == 429 for r in responses)
        
        if rate_limited:
            print("  ✅ Rate limiting détecté (status 429)")
        else:
            print("  ⚠️ Aucun rate limiting détecté")
        
        return {
            'rate_limited': rate_limited,
            'responses': responses
        }
        
    except Exception as e:
        print(f"  ❌ Erreur lors du test de rate limiting: {e}")
        return {}

def test_error_handling():
    """Test de la gestion d'erreurs"""
    print("\n🚫 Test de la gestion d'erreurs...")
    
    test_cases = [
        {
            'name': 'Endpoint inexistant',
            'url': f"{BASE_URL}/api/v1/nonexistent/",
            'expected_status': 404
        },
        {
            'name': 'Méthode non autorisée',
            'url': f"{BASE_URL}/api/v1/",
            'method': 'DELETE',
            'expected_status': 405
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        try:
            method = test_case.get('method', 'GET')
            if method == 'GET':
                response = requests.get(test_case['url'], timeout=5)
            elif method == 'DELETE':
                response = requests.delete(test_case['url'], timeout=5)
            
            expected = test_case['expected_status']
            actual = response.status_code
            
            status = "✅" if actual == expected else "❌"
            print(f"  {status} {test_case['name']}: {actual} (attendu: {expected})")
            
            results.append({
                'name': test_case['name'],
                'expected_status': expected,
                'actual_status': actual,
                'passed': actual == expected
            })
            
        except Exception as e:
            print(f"  ❌ {test_case['name']}: Erreur - {e}")
            results.append({
                'name': test_case['name'],
                'error': str(e),
                'passed': False
            })
    
    return results

def test_api_documentation():
    """Test de la documentation API"""
    print("\n📚 Test de la documentation API...")
    
    doc_endpoints = [
        "/api/schema/",
        "/api/docs/",
        "/api/redoc/",
    ]
    
    results = []
    
    for endpoint in doc_endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
            accessible = response.status_code == 200
            
            status = "✅" if accessible else "❌"
            print(f"  {status} {endpoint}: {response.status_code}")
            
            results.append({
                'endpoint': endpoint,
                'accessible': accessible,
                'status_code': response.status_code
            })
            
        except Exception as e:
            print(f"  ❌ {endpoint}: Erreur - {e}")
            results.append({
                'endpoint': endpoint,
                'accessible': False,
                'error': str(e)
            })
    
    return results

def test_cors_configuration():
    """Test de la configuration CORS"""
    print("\n🌍 Test de la configuration CORS...")
    
    try:
        # Test d'une requête OPTIONS (preflight)
        response = requests.options(f"{BASE_URL}/api/v1/", timeout=5)
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
        }
        
        for header, value in cors_headers.items():
            status = "✅" if value else "❌"
            print(f"  {status} {header}: {value or 'Missing'}")
        
        return {
            'cors_configured': any(cors_headers.values()),
            'headers': cors_headers
        }
        
    except Exception as e:
        print(f"  ❌ Erreur lors du test CORS: {e}")
        return {}

def generate_report(all_results):
    """Générer un rapport de test"""
    print("\n" + "="*60)
    print("📊 RAPPORT DE TEST DE L'API DE SÉCURITÉ")
    print("="*60)
    
    total_tests = 0
    passed_tests = 0
    
    # Compter les tests réussis
    for category, results in all_results.items():
        if isinstance(results, list):
            for result in results:
                total_tests += 1
                if result.get('accessible', False) or result.get('passed', False):
                    passed_tests += 1
        elif isinstance(results, dict):
            if 'responses' in results:  # Rate limiting
                total_tests += 1
                if results.get('rate_limited', False):
                    passed_tests += 1
            elif 'cors_configured' in results:  # CORS
                total_tests += 1
                if results.get('cors_configured', False):
                    passed_tests += 1
            else:  # Security headers
                for header, data in results.items():
                    total_tests += 1
                    if data.get('present', False):
                        passed_tests += 1
    
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    print(f"📈 Taux de réussite: {success_rate:.1f}% ({passed_tests}/{total_tests})")
    print(f"⏰ Date du test: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success_rate >= 80:
        print("\n🎉 L'API de sécurité fonctionne correctement!")
    elif success_rate >= 60:
        print("\n⚠️ L'API fonctionne mais nécessite des améliorations.")
    else:
        print("\n❌ L'API nécessite des corrections importantes.")
    
    return {
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'success_rate': success_rate,
        'timestamp': datetime.now().isoformat()
    }

def main():
    """Fonction principale"""
    print("🚀 Démarrage des tests de l'API de sécurité TrustVault")
    print("🌐 URL de base:", BASE_URL)
    print("="*60)
    
    # Vérifier que le serveur est accessible
    try:
        response = requests.get(BASE_URL, timeout=5)
        print(f"✅ Serveur accessible (Status: {response.status_code})")
    except Exception as e:
        print(f"❌ Serveur inaccessible: {e}")
        print("🔧 Assurez-vous que le serveur Django est démarré avec: python manage.py runserver")
        return False
    
    # Exécuter tous les tests
    all_results = {}
    
    all_results['endpoints'] = test_api_endpoints()
    all_results['security_headers'] = test_security_headers()
    all_results['rate_limiting'] = test_rate_limiting()
    all_results['error_handling'] = test_error_handling()
    all_results['api_documentation'] = test_api_documentation()
    all_results['cors'] = test_cors_configuration()
    
    # Générer le rapport
    report = generate_report(all_results)
    
    # Sauvegarder les résultats
    with open('security_api_test_results.json', 'w') as f:
        json.dump({
            'report': report,
            'detailed_results': all_results
        }, f, indent=2, default=str)
    
    print(f"\n💾 Résultats sauvegardés dans: security_api_test_results.json")
    
    return report['success_rate'] >= 80

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
