{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\pages\\\\Portfolio\\\\AnalyticsPage.tsx\",\n  _s = $RefreshSig$();\n// TrustVault - Portfolio Analytics Page\n\nimport React, { useState } from 'react';\nimport { Box, Typography, Button, Grid, Card, CardContent, Paper, Chip, FormControl, InputLabel, Select, MenuItem, LinearProgress } from '@mui/material';\nimport { ArrowBack, TrendingUp, TrendingDown, PieChart, ShowChart, Assessment } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useQuery } from 'react-query';\n\n// Services\nimport apiService from '../../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AnalyticsPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    id: portfolioId\n  } = useParams();\n  const [timeRange, setTimeRange] = useState('1M');\n\n  // Fetch portfolio details\n  const {\n    data: portfolio\n  } = useQuery(['portfolio', portfolioId], () => apiService.getPortfolio(portfolioId), {\n    enabled: !!portfolioId\n  });\n\n  // Fetch analytics data\n  const {\n    data: analytics,\n    isLoading: analyticsLoading\n  } = useQuery(['analytics', portfolioId], () => apiService.getPortfolioAnalytics(portfolioId), {\n    enabled: !!portfolioId\n  });\n\n  // Fetch holdings for allocation analysis\n  const {\n    data: holdings\n  } = useQuery(['holdings', portfolioId], () => apiService.getHoldings(portfolioId), {\n    enabled: !!portfolioId\n  });\n  const formatCurrency = value => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(typeof value === 'string' ? parseFloat(value) : value);\n  };\n  const formatPercentage = value => {\n    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;\n  };\n\n  // Calculate portfolio metrics\n  const totalValue = portfolio ? parseFloat(portfolio.total_value) : 0;\n  const mockMetrics = {\n    totalReturn: 1250.75,\n    totalReturnPercent: 8.45,\n    dayChange: -45.32,\n    dayChangePercent: -0.32,\n    weekChange: 125.50,\n    weekChangePercent: 0.89,\n    monthChange: 450.25,\n    monthChangePercent: 3.21\n  };\n\n  // Mock allocation data\n  const allocationData = (holdings === null || holdings === void 0 ? void 0 : holdings.map(holding => ({\n    symbol: holding.asset.symbol,\n    name: holding.asset.name,\n    value: parseFloat(holding.current_value),\n    percentage: parseFloat(holding.current_value) / totalValue * 100,\n    sector: holding.asset.sector\n  }))) || [];\n\n  // Group by sector\n  const sectorAllocation = allocationData.reduce((acc, holding) => {\n    const sector = holding.sector || 'Other';\n    if (!(sector in acc)) {\n      acc[sector] = {\n        value: 0,\n        percentage: 0\n      };\n    }\n    acc[sector].value += holding.value;\n    acc[sector].percentage += holding.percentage;\n    return acc;\n  }, {});\n  if (!portfolioId) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: 1200,\n        mx: 'auto',\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"error\",\n        children: \"Portfolio ID is required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Analytics - TrustVault\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Portfolio performance analytics and insights\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: 1200,\n        mx: 'auto',\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 26\n            }, this),\n            onClick: () => navigate(`/portfolios/${portfolioId}`),\n            sx: {\n              mr: 2\n            },\n            children: \"Back to Portfolio\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              component: \"h1\",\n              children: \"Portfolio Analytics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), portfolio && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: portfolio.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          size: \"small\",\n          sx: {\n            minWidth: 120\n          },\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"Time Range\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: timeRange,\n            onChange: e => setTimeRange(e.target.value),\n            label: \"Time Range\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"1D\",\n              children: \"1 Day\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"1W\",\n              children: \"1 Week\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"1M\",\n              children: \"1 Month\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"3M\",\n              children: \"3 Months\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"6M\",\n              children: \"6 Months\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"1Y\",\n              children: \"1 Year\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"ALL\",\n              children: \"All Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Performance Overview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 3,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 3,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  variant: \"outlined\",\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      mb: 1,\n                      children: [/*#__PURE__*/_jsxDEV(Assessment, {\n                        color: \"primary\",\n                        sx: {\n                          mr: 1\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 181,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Total Value\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 182,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      fontWeight: \"bold\",\n                      children: formatCurrency(totalValue)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 3,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  variant: \"outlined\",\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      mb: 1,\n                      children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                        color: \"success\",\n                        sx: {\n                          mr: 1\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 197,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Total Return\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 198,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      fontWeight: \"bold\",\n                      color: \"success.main\",\n                      children: formatCurrency(mockMetrics.totalReturn)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"success.main\",\n                      children: formatPercentage(mockMetrics.totalReturnPercent)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 205,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 3,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  variant: \"outlined\",\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      mb: 1,\n                      children: [/*#__PURE__*/_jsxDEV(TrendingDown, {\n                        color: \"error\",\n                        sx: {\n                          mr: 1\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 216,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Day Change\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 217,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 215,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      fontWeight: \"bold\",\n                      color: \"error.main\",\n                      children: formatCurrency(mockMetrics.dayChange)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"error.main\",\n                      children: formatPercentage(mockMetrics.dayChangePercent)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 3,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  variant: \"outlined\",\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      mb: 1,\n                      children: [/*#__PURE__*/_jsxDEV(ShowChart, {\n                        color: \"info\",\n                        sx: {\n                          mr: 1\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 235,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Month Change\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 236,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 234,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      fontWeight: \"bold\",\n                      color: \"success.main\",\n                      children: formatCurrency(mockMetrics.monthChange)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 240,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"success.main\",\n                      children: formatPercentage(mockMetrics.monthChangePercent)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(PieChart, {\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                children: \"Asset Allocation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), allocationData.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              py: 4,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"No holdings to display\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              children: allocationData.slice(0, 5).map((holding, index) => /*#__PURE__*/_jsxDEV(Box, {\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  mb: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: holding.symbol\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: holding.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 278,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    textAlign: \"right\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: [holding.percentage.toFixed(1), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 283,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: formatCurrency(holding.value)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: holding.percentage,\n                  sx: {\n                    height: 6,\n                    borderRadius: 3\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 23\n                }, this)]\n              }, holding.symbol, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Sector Allocation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), Object.keys(sectorAllocation).length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              py: 4,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"No sector data available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              children: Object.entries(sectorAllocation).map(([sector, data]) => /*#__PURE__*/_jsxDEV(Box, {\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  mb: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    children: sector\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    textAlign: \"right\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: [data.percentage.toFixed(1), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: formatCurrency(data.value)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 328,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: data.percentage,\n                  sx: {\n                    height: 6,\n                    borderRadius: 3\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 23\n                }, this)]\n              }, sector, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Performance Chart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                height: 300,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                bgcolor: 'background.default',\n                borderRadius: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                textAlign: \"center\",\n                children: [/*#__PURE__*/_jsxDEV(ShowChart, {\n                  sx: {\n                    fontSize: 48,\n                    color: 'text.secondary',\n                    mb: 2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: \"Performance Chart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Interactive chart showing portfolio performance over time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Coming Soon\",\n                  color: \"primary\",\n                  size: \"small\",\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AnalyticsPage, \"R0DbjeKYg/zjyFDFmN8wiw0ZK40=\", false, function () {\n  return [useNavigate, useParams, useQuery, useQuery, useQuery];\n});\n_c = AnalyticsPage;\nexport default AnalyticsPage;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsPage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Paper", "Chip", "FormControl", "InputLabel", "Select", "MenuItem", "LinearProgress", "ArrowBack", "TrendingUp", "TrendingDown", "<PERSON><PERSON><PERSON>", "ShowChart", "Assessment", "<PERSON><PERSON><PERSON>", "useNavigate", "useParams", "useQuery", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AnalyticsPage", "_s", "navigate", "id", "portfolioId", "timeRange", "setTimeRange", "data", "portfolio", "getPortfolio", "enabled", "analytics", "isLoading", "analyticsLoading", "getPortfolioAnalytics", "holdings", "getHoldings", "formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "format", "parseFloat", "formatPercentage", "toFixed", "totalValue", "total_value", "mockMetrics", "totalReturn", "totalReturnPercent", "day<PERSON><PERSON>e", "dayChangePercent", "weekChange", "weekChangePercent", "monthChange", "monthChangePercent", "allocationData", "map", "holding", "symbol", "asset", "name", "current_value", "percentage", "sector", "sectorAllocation", "reduce", "acc", "sx", "max<PERSON><PERSON><PERSON>", "mx", "p", "children", "variant", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "display", "alignItems", "justifyContent", "mb", "startIcon", "onClick", "mr", "component", "size", "min<PERSON><PERSON><PERSON>", "onChange", "e", "target", "label", "container", "spacing", "item", "xs", "gutterBottom", "sm", "md", "fontWeight", "length", "textAlign", "py", "slice", "index", "height", "borderRadius", "Object", "keys", "entries", "bgcolor", "fontSize", "mt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/pages/Portfolio/AnalyticsPage.tsx"], "sourcesContent": ["// TrustVault - Portfolio Analytics Page\n\nimport React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Paper,\n  Chip,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  LinearProgress,\n} from '@mui/material';\nimport {\n  ArrowBack,\n  TrendingUp,\n  TrendingDown,\n  PieChart,\n  ShowChart,\n  Assessment,\n} from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useQuery } from 'react-query';\n\n// Services\nimport apiService from '../../services/api';\n\nconst AnalyticsPage: React.FC = () => {\n  const navigate = useNavigate();\n  const { id: portfolioId } = useParams<{ id: string }>();\n  \n  const [timeRange, setTimeRange] = useState<string>('1M');\n\n  // Fetch portfolio details\n  const { data: portfolio } = useQuery(\n    ['portfolio', portfolioId],\n    () => apiService.getPortfolio(portfolioId!),\n    {\n      enabled: !!portfolioId,\n    }\n  );\n\n  // Fetch analytics data\n  const { data: analytics, isLoading: analyticsLoading } = useQuery(\n    ['analytics', portfolioId],\n    () => apiService.getPortfolioAnalytics(portfolioId!),\n    {\n      enabled: !!portfolioId,\n    }\n  );\n\n  // Fetch holdings for allocation analysis\n  const { data: holdings } = useQuery(\n    ['holdings', portfolioId],\n    () => apiService.getHoldings(portfolioId!),\n    {\n      enabled: !!portfolioId,\n    }\n  );\n\n  const formatCurrency = (value: string | number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(typeof value === 'string' ? parseFloat(value) : value);\n  };\n\n  const formatPercentage = (value: number) => {\n    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;\n  };\n\n  // Calculate portfolio metrics\n  const totalValue = portfolio ? parseFloat(portfolio.total_value) : 0;\n  const mockMetrics = {\n    totalReturn: 1250.75,\n    totalReturnPercent: 8.45,\n    dayChange: -45.32,\n    dayChangePercent: -0.32,\n    weekChange: 125.50,\n    weekChangePercent: 0.89,\n    monthChange: 450.25,\n    monthChangePercent: 3.21,\n  };\n\n  // Mock allocation data\n  const allocationData = holdings?.map((holding: any) => ({\n    symbol: holding.asset.symbol,\n    name: holding.asset.name,\n    value: parseFloat(holding.current_value),\n    percentage: (parseFloat(holding.current_value) / totalValue) * 100,\n    sector: holding.asset.sector,\n  })) || [];\n\n  // Group by sector\n  const sectorAllocation = allocationData.reduce((acc: Record<string, any>, holding: any) => {\n    const sector = holding.sector || 'Other';\n    if (!(sector in acc)) {\n      acc[sector] = { value: 0, percentage: 0 };\n    }\n    acc[sector].value += holding.value;\n    acc[sector].percentage += holding.percentage;\n    return acc;\n  }, {} as Record<string, any>);\n\n  if (!portfolioId) {\n    return (\n      <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>\n        <Typography variant=\"h6\" color=\"error\">\n          Portfolio ID is required\n        </Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <>\n      <Helmet>\n        <title>Analytics - TrustVault</title>\n        <meta name=\"description\" content=\"Portfolio performance analytics and insights\" />\n      </Helmet>\n\n      <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>\n        {/* Header */}\n        <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\" mb={3}>\n          <Box display=\"flex\" alignItems=\"center\">\n            <Button\n              startIcon={<ArrowBack />}\n              onClick={() => navigate(`/portfolios/${portfolioId}`)}\n              sx={{ mr: 2 }}\n            >\n              Back to Portfolio\n            </Button>\n            <Box>\n              <Typography variant=\"h4\" component=\"h1\">\n                Portfolio Analytics\n              </Typography>\n              {portfolio && (\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  {portfolio.name}\n                </Typography>\n              )}\n            </Box>\n          </Box>\n          \n          <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n            <InputLabel>Time Range</InputLabel>\n            <Select\n              value={timeRange}\n              onChange={(e) => setTimeRange(e.target.value)}\n              label=\"Time Range\"\n            >\n              <MenuItem value=\"1D\">1 Day</MenuItem>\n              <MenuItem value=\"1W\">1 Week</MenuItem>\n              <MenuItem value=\"1M\">1 Month</MenuItem>\n              <MenuItem value=\"3M\">3 Months</MenuItem>\n              <MenuItem value=\"6M\">6 Months</MenuItem>\n              <MenuItem value=\"1Y\">1 Year</MenuItem>\n              <MenuItem value=\"ALL\">All Time</MenuItem>\n            </Select>\n          </FormControl>\n        </Box>\n\n        <Grid container spacing={3}>\n          {/* Performance Overview */}\n          <Grid item xs={12}>\n            <Paper sx={{ p: 3 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                Performance Overview\n              </Typography>\n              <Grid container spacing={3}>\n                <Grid item xs={12} sm={6} md={3}>\n                  <Card variant=\"outlined\">\n                    <CardContent>\n                      <Box display=\"flex\" alignItems=\"center\" mb={1}>\n                        <Assessment color=\"primary\" sx={{ mr: 1 }} />\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Total Value\n                        </Typography>\n                      </Box>\n                      <Typography variant=\"h5\" fontWeight=\"bold\">\n                        {formatCurrency(totalValue)}\n                      </Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n                \n                <Grid item xs={12} sm={6} md={3}>\n                  <Card variant=\"outlined\">\n                    <CardContent>\n                      <Box display=\"flex\" alignItems=\"center\" mb={1}>\n                        <TrendingUp color=\"success\" sx={{ mr: 1 }} />\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Total Return\n                        </Typography>\n                      </Box>\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"success.main\">\n                        {formatCurrency(mockMetrics.totalReturn)}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"success.main\">\n                        {formatPercentage(mockMetrics.totalReturnPercent)}\n                      </Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n                \n                <Grid item xs={12} sm={6} md={3}>\n                  <Card variant=\"outlined\">\n                    <CardContent>\n                      <Box display=\"flex\" alignItems=\"center\" mb={1}>\n                        <TrendingDown color=\"error\" sx={{ mr: 1 }} />\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Day Change\n                        </Typography>\n                      </Box>\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"error.main\">\n                        {formatCurrency(mockMetrics.dayChange)}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"error.main\">\n                        {formatPercentage(mockMetrics.dayChangePercent)}\n                      </Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n                \n                <Grid item xs={12} sm={6} md={3}>\n                  <Card variant=\"outlined\">\n                    <CardContent>\n                      <Box display=\"flex\" alignItems=\"center\" mb={1}>\n                        <ShowChart color=\"info\" sx={{ mr: 1 }} />\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Month Change\n                        </Typography>\n                      </Box>\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"success.main\">\n                        {formatCurrency(mockMetrics.monthChange)}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"success.main\">\n                        {formatPercentage(mockMetrics.monthChangePercent)}\n                      </Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              </Grid>\n            </Paper>\n          </Grid>\n\n          {/* Asset Allocation */}\n          <Grid item xs={12} md={6}>\n            <Paper sx={{ p: 3 }}>\n              <Box display=\"flex\" alignItems=\"center\" mb={3}>\n                <PieChart color=\"primary\" sx={{ mr: 1 }} />\n                <Typography variant=\"h6\">\n                  Asset Allocation\n                </Typography>\n              </Box>\n              \n              {allocationData.length === 0 ? (\n                <Box textAlign=\"center\" py={4}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    No holdings to display\n                  </Typography>\n                </Box>\n              ) : (\n                <Box>\n                  {allocationData.slice(0, 5).map((holding: any, index: number) => (\n                    <Box key={holding.symbol} mb={2}>\n                      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={1}>\n                        <Box>\n                          <Typography variant=\"body2\" fontWeight=\"medium\">\n                            {holding.symbol}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            {holding.name}\n                          </Typography>\n                        </Box>\n                        <Box textAlign=\"right\">\n                          <Typography variant=\"body2\" fontWeight=\"medium\">\n                            {holding.percentage.toFixed(1)}%\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            {formatCurrency(holding.value)}\n                          </Typography>\n                        </Box>\n                      </Box>\n                      <LinearProgress\n                        variant=\"determinate\"\n                        value={holding.percentage}\n                        sx={{ height: 6, borderRadius: 3 }}\n                      />\n                    </Box>\n                  ))}\n                </Box>\n              )}\n            </Paper>\n          </Grid>\n\n          {/* Sector Allocation */}\n          <Grid item xs={12} md={6}>\n            <Paper sx={{ p: 3 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                Sector Allocation\n              </Typography>\n              \n              {Object.keys(sectorAllocation).length === 0 ? (\n                <Box textAlign=\"center\" py={4}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    No sector data available\n                  </Typography>\n                </Box>\n              ) : (\n                <Box>\n                  {Object.entries(sectorAllocation).map(([sector, data]: [string, any]) => (\n                    <Box key={sector} mb={2}>\n                      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={1}>\n                        <Typography variant=\"body2\" fontWeight=\"medium\">\n                          {sector}\n                        </Typography>\n                        <Box textAlign=\"right\">\n                          <Typography variant=\"body2\" fontWeight=\"medium\">\n                            {data.percentage.toFixed(1)}%\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            {formatCurrency(data.value)}\n                          </Typography>\n                        </Box>\n                      </Box>\n                      <LinearProgress\n                        variant=\"determinate\"\n                        value={data.percentage}\n                        sx={{ height: 6, borderRadius: 3 }}\n                      />\n                    </Box>\n                  ))}\n                </Box>\n              )}\n            </Paper>\n          </Grid>\n\n          {/* Performance Chart Placeholder */}\n          <Grid item xs={12}>\n            <Paper sx={{ p: 3 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                Performance Chart\n              </Typography>\n              <Box\n                sx={{\n                  height: 300,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  bgcolor: 'background.default',\n                  borderRadius: 1,\n                }}\n              >\n                <Box textAlign=\"center\">\n                  <ShowChart sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />\n                  <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                    Performance Chart\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Interactive chart showing portfolio performance over time\n                  </Typography>\n                  <Chip label=\"Coming Soon\" color=\"primary\" size=\"small\" sx={{ mt: 1 }} />\n                </Box>\n              </Box>\n            </Paper>\n          </Grid>\n        </Grid>\n      </Box>\n    </>\n  );\n};\n\nexport default AnalyticsPage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,QACT,eAAe;AACtB,SACEC,SAAS,EACTC,UAAU,EACVC,YAAY,EACZC,QAAQ,EACRC,SAAS,EACTC,UAAU,QACL,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,QAAQ,QAAQ,aAAa;;AAEtC;AACA,OAAOC,UAAU,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW,EAAE,EAAEC;EAAY,CAAC,GAAGX,SAAS,CAAiB,CAAC;EAEvD,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAS,IAAI,CAAC;;EAExD;EACA,MAAM;IAAEoC,IAAI,EAAEC;EAAU,CAAC,GAAGd,QAAQ,CAClC,CAAC,WAAW,EAAEU,WAAW,CAAC,EAC1B,MAAMT,UAAU,CAACc,YAAY,CAACL,WAAY,CAAC,EAC3C;IACEM,OAAO,EAAE,CAAC,CAACN;EACb,CACF,CAAC;;EAED;EACA,MAAM;IAAEG,IAAI,EAAEI,SAAS;IAAEC,SAAS,EAAEC;EAAiB,CAAC,GAAGnB,QAAQ,CAC/D,CAAC,WAAW,EAAEU,WAAW,CAAC,EAC1B,MAAMT,UAAU,CAACmB,qBAAqB,CAACV,WAAY,CAAC,EACpD;IACEM,OAAO,EAAE,CAAC,CAACN;EACb,CACF,CAAC;;EAED;EACA,MAAM;IAAEG,IAAI,EAAEQ;EAAS,CAAC,GAAGrB,QAAQ,CACjC,CAAC,UAAU,EAAEU,WAAW,CAAC,EACzB,MAAMT,UAAU,CAACqB,WAAW,CAACZ,WAAY,CAAC,EAC1C;IACEM,OAAO,EAAE,CAAC,CAACN;EACb,CACF,CAAC;EAED,MAAMa,cAAc,GAAIC,KAAsB,IAAK;IACjD,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAAC,OAAOL,KAAK,KAAK,QAAQ,GAAGM,UAAU,CAACN,KAAK,CAAC,GAAGA,KAAK,CAAC;EAClE,CAAC;EAED,MAAMO,gBAAgB,GAAIP,KAAa,IAAK;IAC1C,OAAO,GAAGA,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGA,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC,GAAG;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGnB,SAAS,GAAGgB,UAAU,CAAChB,SAAS,CAACoB,WAAW,CAAC,GAAG,CAAC;EACpE,MAAMC,WAAW,GAAG;IAClBC,WAAW,EAAE,OAAO;IACpBC,kBAAkB,EAAE,IAAI;IACxBC,SAAS,EAAE,CAAC,KAAK;IACjBC,gBAAgB,EAAE,CAAC,IAAI;IACvBC,UAAU,EAAE,MAAM;IAClBC,iBAAiB,EAAE,IAAI;IACvBC,WAAW,EAAE,MAAM;IACnBC,kBAAkB,EAAE;EACtB,CAAC;;EAED;EACA,MAAMC,cAAc,GAAG,CAAAvB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwB,GAAG,CAAEC,OAAY,KAAM;IACtDC,MAAM,EAAED,OAAO,CAACE,KAAK,CAACD,MAAM;IAC5BE,IAAI,EAAEH,OAAO,CAACE,KAAK,CAACC,IAAI;IACxBzB,KAAK,EAAEM,UAAU,CAACgB,OAAO,CAACI,aAAa,CAAC;IACxCC,UAAU,EAAGrB,UAAU,CAACgB,OAAO,CAACI,aAAa,CAAC,GAAGjB,UAAU,GAAI,GAAG;IAClEmB,MAAM,EAAEN,OAAO,CAACE,KAAK,CAACI;EACxB,CAAC,CAAC,CAAC,KAAI,EAAE;;EAET;EACA,MAAMC,gBAAgB,GAAGT,cAAc,CAACU,MAAM,CAAC,CAACC,GAAwB,EAAET,OAAY,KAAK;IACzF,MAAMM,MAAM,GAAGN,OAAO,CAACM,MAAM,IAAI,OAAO;IACxC,IAAI,EAAEA,MAAM,IAAIG,GAAG,CAAC,EAAE;MACpBA,GAAG,CAACH,MAAM,CAAC,GAAG;QAAE5B,KAAK,EAAE,CAAC;QAAE2B,UAAU,EAAE;MAAE,CAAC;IAC3C;IACAI,GAAG,CAACH,MAAM,CAAC,CAAC5B,KAAK,IAAIsB,OAAO,CAACtB,KAAK;IAClC+B,GAAG,CAACH,MAAM,CAAC,CAACD,UAAU,IAAIL,OAAO,CAACK,UAAU;IAC5C,OAAOI,GAAG;EACZ,CAAC,EAAE,CAAC,CAAwB,CAAC;EAE7B,IAAI,CAAC7C,WAAW,EAAE;IAChB,oBACEP,OAAA,CAACzB,GAAG;MAAC8E,EAAE,EAAE;QAAEC,QAAQ,EAAE,IAAI;QAAEC,EAAE,EAAE,MAAM;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,eAC5CzD,OAAA,CAACxB,UAAU;QAACkF,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,OAAO;QAAAF,QAAA,EAAC;MAEvC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACE/D,OAAA,CAAAE,SAAA;IAAAuD,QAAA,gBACEzD,OAAA,CAACN,MAAM;MAAA+D,QAAA,gBACLzD,OAAA;QAAAyD,QAAA,EAAO;MAAsB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrC/D,OAAA;QAAM8C,IAAI,EAAC,aAAa;QAACkB,OAAO,EAAC;MAA8C;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5E,CAAC,eAET/D,OAAA,CAACzB,GAAG;MAAC8E,EAAE,EAAE;QAAEC,QAAQ,EAAE,IAAI;QAAEC,EAAE,EAAE,MAAM;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAE5CzD,OAAA,CAACzB,GAAG;QAAC0F,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,cAAc,EAAC,eAAe;QAACC,EAAE,EAAE,CAAE;QAAAX,QAAA,gBAC3EzD,OAAA,CAACzB,GAAG;UAAC0F,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAAAT,QAAA,gBACrCzD,OAAA,CAACvB,MAAM;YACL4F,SAAS,eAAErE,OAAA,CAACZ,SAAS;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBO,OAAO,EAAEA,CAAA,KAAMjE,QAAQ,CAAC,eAAeE,WAAW,EAAE,CAAE;YACtD8C,EAAE,EAAE;cAAEkB,EAAE,EAAE;YAAE,CAAE;YAAAd,QAAA,EACf;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/D,OAAA,CAACzB,GAAG;YAAAkF,QAAA,gBACFzD,OAAA,CAACxB,UAAU;cAACkF,OAAO,EAAC,IAAI;cAACc,SAAS,EAAC,IAAI;cAAAf,QAAA,EAAC;YAExC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZpD,SAAS,iBACRX,OAAA,CAACxB,UAAU;cAACkF,OAAO,EAAC,OAAO;cAACC,KAAK,EAAC,gBAAgB;cAAAF,QAAA,EAC/C9C,SAAS,CAACmC;YAAI;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/D,OAAA,CAACjB,WAAW;UAAC0F,IAAI,EAAC,OAAO;UAACpB,EAAE,EAAE;YAAEqB,QAAQ,EAAE;UAAI,CAAE;UAAAjB,QAAA,gBAC9CzD,OAAA,CAAChB,UAAU;YAAAyE,QAAA,EAAC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnC/D,OAAA,CAACf,MAAM;YACLoC,KAAK,EAAEb,SAAU;YACjBmE,QAAQ,EAAGC,CAAC,IAAKnE,YAAY,CAACmE,CAAC,CAACC,MAAM,CAACxD,KAAK,CAAE;YAC9CyD,KAAK,EAAC,YAAY;YAAArB,QAAA,gBAElBzD,OAAA,CAACd,QAAQ;cAACmC,KAAK,EAAC,IAAI;cAAAoC,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACrC/D,OAAA,CAACd,QAAQ;cAACmC,KAAK,EAAC,IAAI;cAAAoC,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtC/D,OAAA,CAACd,QAAQ;cAACmC,KAAK,EAAC,IAAI;cAAAoC,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACvC/D,OAAA,CAACd,QAAQ;cAACmC,KAAK,EAAC,IAAI;cAAAoC,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxC/D,OAAA,CAACd,QAAQ;cAACmC,KAAK,EAAC,IAAI;cAAAoC,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxC/D,OAAA,CAACd,QAAQ;cAACmC,KAAK,EAAC,IAAI;cAAAoC,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtC/D,OAAA,CAACd,QAAQ;cAACmC,KAAK,EAAC,KAAK;cAAAoC,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAEN/D,OAAA,CAACtB,IAAI;QAACqG,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAvB,QAAA,gBAEzBzD,OAAA,CAACtB,IAAI;UAACuG,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAzB,QAAA,eAChBzD,OAAA,CAACnB,KAAK;YAACwE,EAAE,EAAE;cAAEG,CAAC,EAAE;YAAE,CAAE;YAAAC,QAAA,gBAClBzD,OAAA,CAACxB,UAAU;cAACkF,OAAO,EAAC,IAAI;cAACyB,YAAY;cAAA1B,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/D,OAAA,CAACtB,IAAI;cAACqG,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAvB,QAAA,gBACzBzD,OAAA,CAACtB,IAAI;gBAACuG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACE,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,eAC9BzD,OAAA,CAACrB,IAAI;kBAAC+E,OAAO,EAAC,UAAU;kBAAAD,QAAA,eACtBzD,OAAA,CAACpB,WAAW;oBAAA6E,QAAA,gBACVzD,OAAA,CAACzB,GAAG;sBAAC0F,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACE,EAAE,EAAE,CAAE;sBAAAX,QAAA,gBAC5CzD,OAAA,CAACP,UAAU;wBAACkE,KAAK,EAAC,SAAS;wBAACN,EAAE,EAAE;0BAAEkB,EAAE,EAAE;wBAAE;sBAAE;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7C/D,OAAA,CAACxB,UAAU;wBAACkF,OAAO,EAAC,OAAO;wBAACC,KAAK,EAAC,gBAAgB;wBAAAF,QAAA,EAAC;sBAEnD;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACN/D,OAAA,CAACxB,UAAU;sBAACkF,OAAO,EAAC,IAAI;sBAAC4B,UAAU,EAAC,MAAM;sBAAA7B,QAAA,EACvCrC,cAAc,CAACU,UAAU;oBAAC;sBAAA8B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEP/D,OAAA,CAACtB,IAAI;gBAACuG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACE,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,eAC9BzD,OAAA,CAACrB,IAAI;kBAAC+E,OAAO,EAAC,UAAU;kBAAAD,QAAA,eACtBzD,OAAA,CAACpB,WAAW;oBAAA6E,QAAA,gBACVzD,OAAA,CAACzB,GAAG;sBAAC0F,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACE,EAAE,EAAE,CAAE;sBAAAX,QAAA,gBAC5CzD,OAAA,CAACX,UAAU;wBAACsE,KAAK,EAAC,SAAS;wBAACN,EAAE,EAAE;0BAAEkB,EAAE,EAAE;wBAAE;sBAAE;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7C/D,OAAA,CAACxB,UAAU;wBAACkF,OAAO,EAAC,OAAO;wBAACC,KAAK,EAAC,gBAAgB;wBAAAF,QAAA,EAAC;sBAEnD;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACN/D,OAAA,CAACxB,UAAU;sBAACkF,OAAO,EAAC,IAAI;sBAAC4B,UAAU,EAAC,MAAM;sBAAC3B,KAAK,EAAC,cAAc;sBAAAF,QAAA,EAC5DrC,cAAc,CAACY,WAAW,CAACC,WAAW;oBAAC;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC,eACb/D,OAAA,CAACxB,UAAU;sBAACkF,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,cAAc;sBAAAF,QAAA,EAC7C7B,gBAAgB,CAACI,WAAW,CAACE,kBAAkB;oBAAC;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEP/D,OAAA,CAACtB,IAAI;gBAACuG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACE,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,eAC9BzD,OAAA,CAACrB,IAAI;kBAAC+E,OAAO,EAAC,UAAU;kBAAAD,QAAA,eACtBzD,OAAA,CAACpB,WAAW;oBAAA6E,QAAA,gBACVzD,OAAA,CAACzB,GAAG;sBAAC0F,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACE,EAAE,EAAE,CAAE;sBAAAX,QAAA,gBAC5CzD,OAAA,CAACV,YAAY;wBAACqE,KAAK,EAAC,OAAO;wBAACN,EAAE,EAAE;0BAAEkB,EAAE,EAAE;wBAAE;sBAAE;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7C/D,OAAA,CAACxB,UAAU;wBAACkF,OAAO,EAAC,OAAO;wBAACC,KAAK,EAAC,gBAAgB;wBAAAF,QAAA,EAAC;sBAEnD;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACN/D,OAAA,CAACxB,UAAU;sBAACkF,OAAO,EAAC,IAAI;sBAAC4B,UAAU,EAAC,MAAM;sBAAC3B,KAAK,EAAC,YAAY;sBAAAF,QAAA,EAC1DrC,cAAc,CAACY,WAAW,CAACG,SAAS;oBAAC;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC,eACb/D,OAAA,CAACxB,UAAU;sBAACkF,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,YAAY;sBAAAF,QAAA,EAC3C7B,gBAAgB,CAACI,WAAW,CAACI,gBAAgB;oBAAC;sBAAAwB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEP/D,OAAA,CAACtB,IAAI;gBAACuG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACE,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,eAC9BzD,OAAA,CAACrB,IAAI;kBAAC+E,OAAO,EAAC,UAAU;kBAAAD,QAAA,eACtBzD,OAAA,CAACpB,WAAW;oBAAA6E,QAAA,gBACVzD,OAAA,CAACzB,GAAG;sBAAC0F,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACE,EAAE,EAAE,CAAE;sBAAAX,QAAA,gBAC5CzD,OAAA,CAACR,SAAS;wBAACmE,KAAK,EAAC,MAAM;wBAACN,EAAE,EAAE;0BAAEkB,EAAE,EAAE;wBAAE;sBAAE;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACzC/D,OAAA,CAACxB,UAAU;wBAACkF,OAAO,EAAC,OAAO;wBAACC,KAAK,EAAC,gBAAgB;wBAAAF,QAAA,EAAC;sBAEnD;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACN/D,OAAA,CAACxB,UAAU;sBAACkF,OAAO,EAAC,IAAI;sBAAC4B,UAAU,EAAC,MAAM;sBAAC3B,KAAK,EAAC,cAAc;sBAAAF,QAAA,EAC5DrC,cAAc,CAACY,WAAW,CAACO,WAAW;oBAAC;sBAAAqB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC,eACb/D,OAAA,CAACxB,UAAU;sBAACkF,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,cAAc;sBAAAF,QAAA,EAC7C7B,gBAAgB,CAACI,WAAW,CAACQ,kBAAkB;oBAAC;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGP/D,OAAA,CAACtB,IAAI;UAACuG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACG,EAAE,EAAE,CAAE;UAAA5B,QAAA,eACvBzD,OAAA,CAACnB,KAAK;YAACwE,EAAE,EAAE;cAAEG,CAAC,EAAE;YAAE,CAAE;YAAAC,QAAA,gBAClBzD,OAAA,CAACzB,GAAG;cAAC0F,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACE,EAAE,EAAE,CAAE;cAAAX,QAAA,gBAC5CzD,OAAA,CAACT,QAAQ;gBAACoE,KAAK,EAAC,SAAS;gBAACN,EAAE,EAAE;kBAAEkB,EAAE,EAAE;gBAAE;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3C/D,OAAA,CAACxB,UAAU;gBAACkF,OAAO,EAAC,IAAI;gBAAAD,QAAA,EAAC;cAEzB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAELtB,cAAc,CAAC8C,MAAM,KAAK,CAAC,gBAC1BvF,OAAA,CAACzB,GAAG;cAACiH,SAAS,EAAC,QAAQ;cAACC,EAAE,EAAE,CAAE;cAAAhC,QAAA,eAC5BzD,OAAA,CAACxB,UAAU;gBAACkF,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAF,QAAA,EAAC;cAEnD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,gBAEN/D,OAAA,CAACzB,GAAG;cAAAkF,QAAA,EACDhB,cAAc,CAACiD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChD,GAAG,CAAC,CAACC,OAAY,EAAEgD,KAAa,kBAC1D3F,OAAA,CAACzB,GAAG;gBAAsB6F,EAAE,EAAE,CAAE;gBAAAX,QAAA,gBAC9BzD,OAAA,CAACzB,GAAG;kBAAC0F,OAAO,EAAC,MAAM;kBAACE,cAAc,EAAC,eAAe;kBAACD,UAAU,EAAC,QAAQ;kBAACE,EAAE,EAAE,CAAE;kBAAAX,QAAA,gBAC3EzD,OAAA,CAACzB,GAAG;oBAAAkF,QAAA,gBACFzD,OAAA,CAACxB,UAAU;sBAACkF,OAAO,EAAC,OAAO;sBAAC4B,UAAU,EAAC,QAAQ;sBAAA7B,QAAA,EAC5Cd,OAAO,CAACC;oBAAM;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACb/D,OAAA,CAACxB,UAAU;sBAACkF,OAAO,EAAC,SAAS;sBAACC,KAAK,EAAC,gBAAgB;sBAAAF,QAAA,EACjDd,OAAO,CAACG;oBAAI;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACN/D,OAAA,CAACzB,GAAG;oBAACiH,SAAS,EAAC,OAAO;oBAAA/B,QAAA,gBACpBzD,OAAA,CAACxB,UAAU;sBAACkF,OAAO,EAAC,OAAO;sBAAC4B,UAAU,EAAC,QAAQ;sBAAA7B,QAAA,GAC5Cd,OAAO,CAACK,UAAU,CAACnB,OAAO,CAAC,CAAC,CAAC,EAAC,GACjC;oBAAA;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb/D,OAAA,CAACxB,UAAU;sBAACkF,OAAO,EAAC,SAAS;sBAACC,KAAK,EAAC,gBAAgB;sBAAAF,QAAA,EACjDrC,cAAc,CAACuB,OAAO,CAACtB,KAAK;oBAAC;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN/D,OAAA,CAACb,cAAc;kBACbuE,OAAO,EAAC,aAAa;kBACrBrC,KAAK,EAAEsB,OAAO,CAACK,UAAW;kBAC1BK,EAAE,EAAE;oBAAEuC,MAAM,EAAE,CAAC;oBAAEC,YAAY,EAAE;kBAAE;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA,GAvBMpB,OAAO,CAACC,MAAM;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwBnB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGP/D,OAAA,CAACtB,IAAI;UAACuG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACG,EAAE,EAAE,CAAE;UAAA5B,QAAA,eACvBzD,OAAA,CAACnB,KAAK;YAACwE,EAAE,EAAE;cAAEG,CAAC,EAAE;YAAE,CAAE;YAAAC,QAAA,gBAClBzD,OAAA,CAACxB,UAAU;cAACkF,OAAO,EAAC,IAAI;cAACyB,YAAY;cAAA1B,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAEZ+B,MAAM,CAACC,IAAI,CAAC7C,gBAAgB,CAAC,CAACqC,MAAM,KAAK,CAAC,gBACzCvF,OAAA,CAACzB,GAAG;cAACiH,SAAS,EAAC,QAAQ;cAACC,EAAE,EAAE,CAAE;cAAAhC,QAAA,eAC5BzD,OAAA,CAACxB,UAAU;gBAACkF,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAF,QAAA,EAAC;cAEnD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,gBAEN/D,OAAA,CAACzB,GAAG;cAAAkF,QAAA,EACDqC,MAAM,CAACE,OAAO,CAAC9C,gBAAgB,CAAC,CAACR,GAAG,CAAC,CAAC,CAACO,MAAM,EAAEvC,IAAI,CAAgB,kBAClEV,OAAA,CAACzB,GAAG;gBAAc6F,EAAE,EAAE,CAAE;gBAAAX,QAAA,gBACtBzD,OAAA,CAACzB,GAAG;kBAAC0F,OAAO,EAAC,MAAM;kBAACE,cAAc,EAAC,eAAe;kBAACD,UAAU,EAAC,QAAQ;kBAACE,EAAE,EAAE,CAAE;kBAAAX,QAAA,gBAC3EzD,OAAA,CAACxB,UAAU;oBAACkF,OAAO,EAAC,OAAO;oBAAC4B,UAAU,EAAC,QAAQ;oBAAA7B,QAAA,EAC5CR;kBAAM;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACb/D,OAAA,CAACzB,GAAG;oBAACiH,SAAS,EAAC,OAAO;oBAAA/B,QAAA,gBACpBzD,OAAA,CAACxB,UAAU;sBAACkF,OAAO,EAAC,OAAO;sBAAC4B,UAAU,EAAC,QAAQ;sBAAA7B,QAAA,GAC5C/C,IAAI,CAACsC,UAAU,CAACnB,OAAO,CAAC,CAAC,CAAC,EAAC,GAC9B;oBAAA;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb/D,OAAA,CAACxB,UAAU;sBAACkF,OAAO,EAAC,SAAS;sBAACC,KAAK,EAAC,gBAAgB;sBAAAF,QAAA,EACjDrC,cAAc,CAACV,IAAI,CAACW,KAAK;oBAAC;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN/D,OAAA,CAACb,cAAc;kBACbuE,OAAO,EAAC,aAAa;kBACrBrC,KAAK,EAAEX,IAAI,CAACsC,UAAW;kBACvBK,EAAE,EAAE;oBAAEuC,MAAM,EAAE,CAAC;oBAAEC,YAAY,EAAE;kBAAE;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA,GAlBMd,MAAM;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmBX,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGP/D,OAAA,CAACtB,IAAI;UAACuG,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAzB,QAAA,eAChBzD,OAAA,CAACnB,KAAK;YAACwE,EAAE,EAAE;cAAEG,CAAC,EAAE;YAAE,CAAE;YAAAC,QAAA,gBAClBzD,OAAA,CAACxB,UAAU;cAACkF,OAAO,EAAC,IAAI;cAACyB,YAAY;cAAA1B,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/D,OAAA,CAACzB,GAAG;cACF8E,EAAE,EAAE;gBACFuC,MAAM,EAAE,GAAG;gBACX3B,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB8B,OAAO,EAAE,oBAAoB;gBAC7BJ,YAAY,EAAE;cAChB,CAAE;cAAApC,QAAA,eAEFzD,OAAA,CAACzB,GAAG;gBAACiH,SAAS,EAAC,QAAQ;gBAAA/B,QAAA,gBACrBzD,OAAA,CAACR,SAAS;kBAAC6D,EAAE,EAAE;oBAAE6C,QAAQ,EAAE,EAAE;oBAAEvC,KAAK,EAAE,gBAAgB;oBAAES,EAAE,EAAE;kBAAE;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnE/D,OAAA,CAACxB,UAAU;kBAACkF,OAAO,EAAC,IAAI;kBAACC,KAAK,EAAC,gBAAgB;kBAACwB,YAAY;kBAAA1B,QAAA,EAAC;gBAE7D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb/D,OAAA,CAACxB,UAAU;kBAACkF,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,EAAC;gBAEnD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb/D,OAAA,CAAClB,IAAI;kBAACgG,KAAK,EAAC,aAAa;kBAACnB,KAAK,EAAC,SAAS;kBAACc,IAAI,EAAC,OAAO;kBAACpB,EAAE,EAAE;oBAAE8C,EAAE,EAAE;kBAAE;gBAAE;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAC3D,EAAA,CAxVID,aAAuB;EAAA,QACVR,WAAW,EACAC,SAAS,EAKTC,QAAQ,EASqBA,QAAQ,EAStCA,QAAQ;AAAA;AAAAuG,EAAA,GAzB/BjG,aAAuB;AA0V7B,eAAeA,aAAa;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}