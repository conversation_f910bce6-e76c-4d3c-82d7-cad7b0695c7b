#!/bin/bash

# TrustVault - Startup Script

set -e

echo "🚀 Starting TrustVault Application..."
echo "=================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Creating from template..."
    cp .env.example .env 2>/dev/null || echo "⚠️  Please create a .env file with your configuration"
fi

# Determine environment
ENVIRONMENT=${1:-development}

echo "📋 Environment: $ENVIRONMENT"

# Start services based on environment
if [ "$ENVIRONMENT" = "production" ]; then
    echo "🏭 Starting production environment..."
    docker-compose -f docker-compose.yml up -d
elif [ "$ENVIRONMENT" = "development" ]; then
    echo "🛠️  Starting development environment..."
    docker-compose -f docker-compose.dev.yml up -d
else
    echo "❌ Invalid environment. Use 'development' or 'production'"
    exit 1
fi

echo ""
echo "⏳ Waiting for services to start..."
sleep 10

# Check service health
echo "🔍 Checking service health..."

# Check backend
if curl -f http://localhost:8000/health/ > /dev/null 2>&1; then
    echo "✅ Backend is healthy"
else
    echo "⚠️  Backend health check failed"
fi

# Check frontend
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ Frontend is healthy"
else
    echo "⚠️  Frontend health check failed"
fi

echo ""
echo "🎉 TrustVault is starting up!"
echo "=================================="
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:8000"
echo "📚 API Docs: http://localhost:8000/api/docs/"
echo "🔐 Admin Panel: http://localhost:8000/admin/"
echo ""
echo "Default admin credentials:"
echo "Email: <EMAIL>"
echo "Password: admin123"
echo ""
echo "📊 To view logs: docker-compose logs -f"
echo "🛑 To stop: docker-compose down"
echo "=================================="
