{"report": {"total_tests": 17, "passed_tests": 14, "success_rate": 82.35294117647058, "timestamp": "2025-07-29T11:49:02.323255"}, "detailed_results": {"endpoints": [{"endpoint": "/api/v1/", "status_code": 200, "accessible": true, "response_time": 0.005255, "response_data": {"name": "TrustVault API", "version": "1.0.0", "description": "Secure Portfolio Management API with Advanced Cybersecurity", "endpoints": {"authentication": "http://127.0.0.1:8000/api/v1/auth/", "portfolio": "http://127.0.0.1:8000/api/v1/portfolio/", "security": "http://127.0.0.1:8000/api/v1/security/", "core": "http://127.0.0.1:8000/api/v1/core/"}, "documentation": {"swagger": "http://127.0.0.1:8000/api/docs/", "redoc": "http://127.0.0.1:8000/api/redoc/", "schema": "http://127.0.0.1:8000/api/schema/"}, "health": "http://127.0.0.1:8000/health/", "timestamp": "2025-07-29T10:49:00.767811+00:00"}}, {"endpoint": "/api/v1/auth/", "status_code": 404, "accessible": false, "response_time": 0.033731}, {"endpoint": "/api/v1/security/", "status_code": 200, "accessible": true, "response_time": 0.039841, "response_data": {"message": "TrustVault Security API", "version": "1.0", "endpoints": {"dashboard": "http://127.0.0.1:8000/api/v1/security/dashboard/", "events": "http://127.0.0.1:8000/api/v1/security/events/", "audit_logs": "http://127.0.0.1:8000/api/v1/security/audit-logs/", "metrics": "http://127.0.0.1:8000/api/v1/security/metrics/", "analyze": "http://127.0.0.1:8000/api/v1/security/analyze/", "compliance": "http://127.0.0.1:8000/api/v1/security/compliance/", "scan": "http://127.0.0.1:8000/api/v1/security/scan/"}, "documentation": {"swagger": "http://127.0.0.1:8000/api/docs/", "redoc": "http://127.0.0.1:8000/api/redoc/", "schema": "http://127.0.0.1:8000/api/schema/"}}}, {"endpoint": "/api/v1/core/", "status_code": 404, "accessible": false, "response_time": 0.035592}, {"endpoint": "/api/v1/portfolio/", "status_code": 401, "accessible": true, "response_time": 0.025144}], "security_headers": {"X-Content-Type-Options": {"present": true, "value": "nosniff", "status": "✅"}, "X-Frame-Options": {"present": true, "value": "DENY", "status": "✅"}, "X-XSS-Protection": {"present": true, "value": "1; mode=block", "status": "✅"}, "Strict-Transport-Security": {"present": true, "value": "max-age=31536000; includeSubDomains; preload", "status": "✅"}, "Content-Security-Policy": {"present": true, "value": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'", "status": "✅"}}, "rate_limiting": {"rate_limited": false, "responses": [{"request_number": 1, "status_code": 200, "headers": {"Date": "<PERSON><PERSON>, 29 Jul 2025 10:49:00 GMT", "Server": "WSGIServer/0.2 CPython/3.11.6", "Content-Type": "application/json", "Allow": "GET, HEAD, OPTIONS", "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block", "Referrer-Policy": "strict-origin-when-cross-origin", "Permissions-Policy": "geolocation=(), microphone=(), camera=()", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Length": "575", "Cross-Origin-Opener-Policy": "same-origin", "Vary": "origin"}}, {"request_number": 2, "status_code": 200, "headers": {"Date": "<PERSON><PERSON>, 29 Jul 2025 10:49:01 GMT", "Server": "WSGIServer/0.2 CPython/3.11.6", "Content-Type": "application/json", "Allow": "GET, HEAD, OPTIONS", "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block", "Referrer-Policy": "strict-origin-when-cross-origin", "Permissions-Policy": "geolocation=(), microphone=(), camera=()", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Length": "575", "Cross-Origin-Opener-Policy": "same-origin", "Vary": "origin"}}, {"request_number": 3, "status_code": 200, "headers": {"Date": "<PERSON><PERSON>, 29 Jul 2025 10:49:01 GMT", "Server": "WSGIServer/0.2 CPython/3.11.6", "Content-Type": "application/json", "Allow": "GET, HEAD, OPTIONS", "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block", "Referrer-Policy": "strict-origin-when-cross-origin", "Permissions-Policy": "geolocation=(), microphone=(), camera=()", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Length": "575", "Cross-Origin-Opener-Policy": "same-origin", "Vary": "origin"}}, {"request_number": 4, "status_code": 200, "headers": {"Date": "<PERSON><PERSON>, 29 Jul 2025 10:49:01 GMT", "Server": "WSGIServer/0.2 CPython/3.11.6", "Content-Type": "application/json", "Allow": "GET, HEAD, OPTIONS", "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block", "Referrer-Policy": "strict-origin-when-cross-origin", "Permissions-Policy": "geolocation=(), microphone=(), camera=()", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Length": "575", "Cross-Origin-Opener-Policy": "same-origin", "Vary": "origin"}}, {"request_number": 5, "status_code": 200, "headers": {"Date": "<PERSON><PERSON>, 29 Jul 2025 10:49:01 GMT", "Server": "WSGIServer/0.2 CPython/3.11.6", "Content-Type": "application/json", "Allow": "GET, HEAD, OPTIONS", "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block", "Referrer-Policy": "strict-origin-when-cross-origin", "Permissions-Policy": "geolocation=(), microphone=(), camera=()", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Length": "575", "Cross-Origin-Opener-Policy": "same-origin", "Vary": "origin"}}, {"request_number": 6, "status_code": 200, "headers": {"Date": "<PERSON><PERSON>, 29 Jul 2025 10:49:01 GMT", "Server": "WSGIServer/0.2 CPython/3.11.6", "Content-Type": "application/json", "Allow": "GET, HEAD, OPTIONS", "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block", "Referrer-Policy": "strict-origin-when-cross-origin", "Permissions-Policy": "geolocation=(), microphone=(), camera=()", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Length": "575", "Cross-Origin-Opener-Policy": "same-origin", "Vary": "origin"}}, {"request_number": 7, "status_code": 200, "headers": {"Date": "<PERSON><PERSON>, 29 Jul 2025 10:49:01 GMT", "Server": "WSGIServer/0.2 CPython/3.11.6", "Content-Type": "application/json", "Allow": "GET, HEAD, OPTIONS", "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block", "Referrer-Policy": "strict-origin-when-cross-origin", "Permissions-Policy": "geolocation=(), microphone=(), camera=()", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Length": "575", "Cross-Origin-Opener-Policy": "same-origin", "Vary": "origin"}}, {"request_number": 8, "status_code": 200, "headers": {"Date": "<PERSON><PERSON>, 29 Jul 2025 10:49:01 GMT", "Server": "WSGIServer/0.2 CPython/3.11.6", "Content-Type": "application/json", "Allow": "GET, HEAD, OPTIONS", "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block", "Referrer-Policy": "strict-origin-when-cross-origin", "Permissions-Policy": "geolocation=(), microphone=(), camera=()", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Length": "575", "Cross-Origin-Opener-Policy": "same-origin", "Vary": "origin"}}, {"request_number": 9, "status_code": 200, "headers": {"Date": "<PERSON><PERSON>, 29 Jul 2025 10:49:01 GMT", "Server": "WSGIServer/0.2 CPython/3.11.6", "Content-Type": "application/json", "Allow": "GET, HEAD, OPTIONS", "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block", "Referrer-Policy": "strict-origin-when-cross-origin", "Permissions-Policy": "geolocation=(), microphone=(), camera=()", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Length": "575", "Cross-Origin-Opener-Policy": "same-origin", "Vary": "origin"}}, {"request_number": 10, "status_code": 200, "headers": {"Date": "<PERSON><PERSON>, 29 Jul 2025 10:49:01 GMT", "Server": "WSGIServer/0.2 CPython/3.11.6", "Content-Type": "application/json", "Allow": "GET, HEAD, OPTIONS", "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block", "Referrer-Policy": "strict-origin-when-cross-origin", "Permissions-Policy": "geolocation=(), microphone=(), camera=()", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Length": "575", "Cross-Origin-Opener-Policy": "same-origin", "Vary": "origin"}}]}, "error_handling": [{"name": "Endpoint inexistant", "expected_status": 404, "actual_status": 404, "passed": true}, {"name": "Méthode non autorisée", "expected_status": 405, "actual_status": 405, "passed": true}], "api_documentation": [{"endpoint": "/api/schema/", "accessible": true, "status_code": 200}, {"endpoint": "/api/docs/", "accessible": true, "status_code": 200}, {"endpoint": "/api/redoc/", "accessible": true, "status_code": 200}], "cors": {"cors_configured": true, "headers": {"Access-Control-Allow-Origin": "http://localhost:3000", "Access-Control-Allow-Methods": "DELETE, GET, OPTIONS, PATCH, POST, PUT", "Access-Control-Allow-Headers": "accept, authorization, content-type, user-agent, x-csrftoken, x-requested-with"}}}}