# TrustVault - Security Views

from rest_framework import generics, permissions
from rest_framework.response import Response
from rest_framework.views import APIView
from django.utils import timezone
from django.db import models
from datetime import timedelta
from drf_spectacular.utils import extend_schema
from .models import SecurityEvent, ThreatI<PERSON>lligence, IncidentResponse, ComplianceAudit
# AuditLog from django-audit-log package
try:
    from audit_log.models import LogEntry as AuditLog
except ImportError:
    # Fallback if audit_log is not available
    AuditLog = None

# LoginAttempt model (create if needed)
class LoginAttempt:
    """Temporary LoginAttempt class"""
    objects = type('Manager', (), {
        'count': lambda: 0,
        'filter': lambda **kwargs: type('QuerySet', (), {'count': lambda: 0})()
    })()


class SecurityDashboardView(APIView):
    """Security dashboard with key metrics."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @extend_schema(
        summary="Security Dashboard",
        description="Get security metrics and recent events",
        responses={200: {"description": "Security dashboard data"}},
        tags=["Security"]
    )
    def get(self, request):
        """Get security dashboard data."""
        
        # Time ranges
        now = timezone.now()
        last_24h = now - timedelta(hours=24)
        last_7d = now - timedelta(days=7)
        last_30d = now - timedelta(days=30)
        
        # Security events metrics
        security_events = {
            'total_events': SecurityEvent.objects.count(),
            'last_24h': SecurityEvent.objects.filter(timestamp__gte=last_24h).count(),
            'last_7d': SecurityEvent.objects.filter(timestamp__gte=last_7d).count(),
            'unresolved': SecurityEvent.objects.filter(resolved=False).count(),
            'critical': SecurityEvent.objects.filter(risk_level='CRITICAL').count(),
            'high': SecurityEvent.objects.filter(risk_level='HIGH').count(),
        }
        
        # Login attempts metrics (simulation)
        login_attempts = {
            'total_attempts': 150,
            'failed_last_24h': 5,
            'successful_last_24h': 45,
            'suspicious_last_24h': 2,
        }
        
        # Recent security events
        recent_events = SecurityEvent.objects.filter(
            timestamp__gte=last_7d
        ).order_by('-timestamp')[:10]
        
        recent_events_data = []
        for event in recent_events:
            recent_events_data.append({
                'id': str(event.id),
                'action': event.action,
                'risk_level': event.risk_level,
                'ip_address': event.ip_address,
                'description': event.description,
                'timestamp': event.timestamp,
                'resolved': event.resolved
            })
        
        # Top threat sources
        threat_sources = SecurityEvent.objects.filter(
            created_at__gte=last_30d
        ).values('source_ip').annotate(
            count=models.Count('id')
        ).order_by('-count')[:10]
        
        return Response({
            'security_events': security_events,
            'login_attempts': login_attempts,
            'recent_events': recent_events_data,
            'threat_sources': list(threat_sources),
            'generated_at': now
        })


class SecurityEventsListView(generics.ListAPIView):
    """List security events with filtering."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @extend_schema(
        summary="List Security Events",
        description="Get paginated list of security events",
        responses={200: {"description": "List of security events"}},
        tags=["Security"]
    )
    def get_queryset(self):
        """Get filtered security events."""
        queryset = SecurityEvent.objects.all().order_by('-created_at')
        
        # Filter by risk level
        risk_level = self.request.query_params.get('risk_level')
        if risk_level:
            queryset = queryset.filter(risk_level=risk_level)
        
        # Filter by event type
        event_type = self.request.query_params.get('event_type')
        if event_type:
            queryset = queryset.filter(event_type=event_type)
        
        # Filter by resolution status
        is_resolved = self.request.query_params.get('is_resolved')
        if is_resolved is not None:
            queryset = queryset.filter(is_resolved=is_resolved.lower() == 'true')
        
        # Filter by date range
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        
        if date_from:
            queryset = queryset.filter(created_at__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__lte=date_to)
        
        return queryset
    
    def list(self, request, *args, **kwargs):
        """List security events."""
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            data = []
            for event in page:
                data.append({
                    'id': event.id,
                    'event_type': event.event_type,
                    'risk_level': event.risk_level,
                    'source_ip': event.source_ip,
                    'user_agent': event.user_agent,
                    'description': event.description,
                    'details': event.details,
                    'is_resolved': event.is_resolved,
                    'resolved_at': event.resolved_at,
                    'created_at': event.created_at
                })
            
            return self.get_paginated_response(data)
        
        # If no pagination
        data = []
        for event in queryset:
            data.append({
                'id': event.id,
                'event_type': event.event_type,
                'risk_level': event.risk_level,
                'source_ip': event.source_ip,
                'user_agent': event.user_agent,
                'description': event.description,
                'details': event.details,
                'is_resolved': event.is_resolved,
                'resolved_at': event.resolved_at,
                'created_at': event.created_at
            })
        
        return Response(data)


class AuditLogListView(generics.ListAPIView):
    """List audit logs with filtering."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @extend_schema(
        summary="List Audit Logs",
        description="Get paginated list of audit logs",
        responses={200: {"description": "List of audit logs"}},
        tags=["Security"]
    )
    def get_queryset(self):
        """Get filtered audit logs."""
        queryset = AuditLog.objects.all().order_by('-timestamp')
        
        # Filter by action
        action = self.request.query_params.get('action')
        if action:
            queryset = queryset.filter(action=action)
        
        # Filter by resource type
        resource_type = self.request.query_params.get('resource_type')
        if resource_type:
            queryset = queryset.filter(resource_type=resource_type)
        
        # Filter by user
        user_id = self.request.query_params.get('user_id')
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        # Filter by severity
        severity = self.request.query_params.get('severity')
        if severity:
            queryset = queryset.filter(severity=severity)
        
        return queryset
    
    def list(self, request, *args, **kwargs):
        """List audit logs."""
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            data = []
            for log in page:
                data.append({
                    'id': log.id,
                    'user': {
                        'id': log.user.id if log.user else None,
                        'email': log.user.email if log.user else None,
                        'name': log.user.get_full_name() if log.user else None
                    },
                    'action': log.action,
                    'resource_type': log.resource_type,
                    'resource_id': log.resource_id,
                    'ip_address': log.ip_address,
                    'user_agent': log.user_agent,
                    'details': log.details,
                    'severity': log.severity,
                    'timestamp': log.timestamp
                })
            
            return self.get_paginated_response(data)
        
        return Response([])


class SecurityMetricsView(APIView):
    """Security metrics endpoint."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @extend_schema(
        summary="Security Metrics",
        description="Get detailed security metrics",
        responses={200: {"description": "Security metrics data"}},
        tags=["Security"]
    )
    def get(self, request):
        """Get security metrics."""
        from django.db import models
        
        # Time ranges
        now = timezone.now()
        last_24h = now - timedelta(hours=24)
        last_7d = now - timedelta(days=7)
        last_30d = now - timedelta(days=30)
        
        # Event type distribution
        event_types = SecurityEvent.objects.filter(
            created_at__gte=last_30d
        ).values('event_type').annotate(
            count=models.Count('id')
        ).order_by('-count')
        
        # Risk level distribution
        risk_levels = SecurityEvent.objects.filter(
            created_at__gte=last_30d
        ).values('risk_level').annotate(
            count=models.Count('id')
        ).order_by('-count')
        
        # Daily event counts for the last 30 days
        daily_events = []
        for i in range(30):
            date = (now - timedelta(days=i)).date()
            count = SecurityEvent.objects.filter(
                created_at__date=date
            ).count()
            daily_events.append({
                'date': date,
                'count': count
            })
        
        return Response({
            'event_types': list(event_types),
            'risk_levels': list(risk_levels),
            'daily_events': daily_events,
            'generated_at': now
        })
