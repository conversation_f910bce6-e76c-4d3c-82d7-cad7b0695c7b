# TrustVault - Security Views

from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.utils import timezone
from django.db import models
from datetime import timedelta
from drf_spectacular.utils import extend_schema
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator
from .models import SecurityEvent, ThreatIntelligence, IncidentResponse, ComplianceAudit
from .advanced_crypto import crypto
from .intrusion_detection import ids
from .compliance import compliance_framework
# AuditLog from django-audit-log package
try:
    from audit_log.models import LogEntry as AuditLog
except ImportError:
    # Fallback if audit_log is not available
    AuditLog = None

# LoginAttempt simulation for metrics
class LoginAttemptMetrics:
    """Login attempt metrics simulation"""
    @staticmethod
    def get_metrics():
        return {
            'total_attempts': 150,
            'failed_last_24h': 5,
            'successful_last_24h': 45,
            'suspicious_last_24h': 2,
        }


@method_decorator(ratelimit(key='ip', rate='100/m', method='GET'), name='get')
class SecurityAPIRootView(APIView):
    """Vue racine de l'API de sécurité"""
    permission_classes = []  # Accès public pour la vue racine

    def get(self, request):
        """Retourne les endpoints disponibles de l'API de sécurité"""

        endpoints = {
            'dashboard': request.build_absolute_uri('dashboard/'),
            'events': request.build_absolute_uri('events/'),
            'audit_logs': request.build_absolute_uri('audit-logs/'),
            'metrics': request.build_absolute_uri('metrics/'),
            'analyze': request.build_absolute_uri('analyze/'),
            'compliance': request.build_absolute_uri('compliance/'),
            'scan': request.build_absolute_uri('scan/'),
        }

        return Response({
            'message': 'TrustVault Security API',
            'version': '1.0',
            'endpoints': endpoints,
            'documentation': {
                'swagger': request.build_absolute_uri('/api/docs/'),
                'redoc': request.build_absolute_uri('/api/redoc/'),
                'schema': request.build_absolute_uri('/api/schema/')
            }
        })


class SecurityDashboardView(APIView):
    """Security dashboard with key metrics."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @extend_schema(
        summary="Security Dashboard",
        description="Get security metrics and recent events",
        responses={200: {"description": "Security dashboard data"}},
        tags=["Security"]
    )
    def get(self, request):
        """Get security dashboard data."""
        
        # Time ranges
        now = timezone.now()
        last_24h = now - timedelta(hours=24)
        last_7d = now - timedelta(days=7)
        last_30d = now - timedelta(days=30)
        
        # Security events metrics
        security_events = {
            'total_events': SecurityEvent.objects.count(),
            'last_24h': SecurityEvent.objects.filter(created_at__gte=last_24h).count(),
            'last_7d': SecurityEvent.objects.filter(created_at__gte=last_7d).count(),
            'unresolved': SecurityEvent.objects.filter(is_resolved=False).count(),
            'critical': SecurityEvent.objects.filter(risk_level='CRITICAL').count(),
            'high': SecurityEvent.objects.filter(risk_level='HIGH').count(),
        }
        
        # Login attempts metrics
        login_attempts = LoginAttemptMetrics.get_metrics()
        
        # Recent security events
        recent_events = SecurityEvent.objects.filter(
            created_at__gte=last_7d
        ).order_by('-created_at')[:10]

        recent_events_data = []
        for event in recent_events:
            recent_events_data.append({
                'id': str(event.id),
                'event_type': event.event_type,
                'risk_level': event.risk_level,
                'source_ip': event.source_ip,
                'description': event.description,
                'created_at': event.created_at,
                'is_resolved': event.is_resolved
            })
        
        # Top threat sources
        threat_sources = SecurityEvent.objects.filter(
            created_at__gte=last_30d
        ).values('source_ip').annotate(
            count=models.Count('id')
        ).order_by('-count')[:10]
        
        return Response({
            'security_events': security_events,
            'login_attempts': login_attempts,
            'recent_events': recent_events_data,
            'threat_sources': list(threat_sources),
            'generated_at': now
        })


class SecurityEventsListView(generics.ListAPIView):
    """List security events with filtering."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @extend_schema(
        summary="List Security Events",
        description="Get paginated list of security events",
        responses={200: {"description": "List of security events"}},
        tags=["Security"]
    )
    def get_queryset(self):
        """Get filtered security events."""
        queryset = SecurityEvent.objects.all().order_by('-created_at')

        # Filter by risk level
        risk_level = self.request.query_params.get('risk_level')
        if risk_level:
            queryset = queryset.filter(risk_level=risk_level)

        # Filter by action (equivalent to event_type)
        action = self.request.query_params.get('action')
        if action:
            queryset = queryset.filter(action=action)

        # Filter by resolution status
        resolved = self.request.query_params.get('resolved')
        if resolved is not None:
            queryset = queryset.filter(resolved=resolved.lower() == 'true')

        # Filter by date range
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')

        if date_from:
            queryset = queryset.filter(created_at__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__lte=date_to)

        return queryset
    
    def list(self, request, *args, **kwargs):
        """List security events."""
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            data = []
            for event in page:
                data.append({
                    'id': str(event.id),
                    'event_type': event.event_type,
                    'risk_level': event.risk_level,
                    'source_ip': event.source_ip,
                    'user_agent': event.user_agent,
                    'description': event.description,
                    'details': event.details,
                    'resolved': event.is_resolved,
                    'resolved_at': event.resolved_at,
                    'timestamp': event.created_at
                })

            return self.get_paginated_response(data)
        
        # If no pagination
        data = []
        for event in queryset:
            data.append({
                'id': str(event.id),
                'event_type': event.event_type,
                'risk_level': event.risk_level,
                'source_ip': event.source_ip,
                'user_agent': event.user_agent,
                'description': event.description,
                'details': event.details,
                'resolved': event.is_resolved,
                'resolved_at': event.resolved_at,
                'timestamp': event.created_at
            })
        
        return Response(data)


class AuditLogListView(generics.ListAPIView):
    """List audit logs with filtering."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @extend_schema(
        summary="List Audit Logs",
        description="Get paginated list of audit logs",
        responses={200: {"description": "List of audit logs"}},
        tags=["Security"]
    )
    def get_queryset(self):
        """Get filtered audit logs."""
        if AuditLog is None:
            return []

        queryset = AuditLog.objects.all().order_by('-timestamp')

        # Filter by action
        action = self.request.query_params.get('action')
        if action:
            queryset = queryset.filter(action=action)

        # Filter by resource type
        resource_type = self.request.query_params.get('resource_type')
        if resource_type:
            queryset = queryset.filter(resource_type=resource_type)

        # Filter by user
        user_id = self.request.query_params.get('user_id')
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        # Filter by severity
        severity = self.request.query_params.get('severity')
        if severity:
            queryset = queryset.filter(severity=severity)

        return queryset
    
    def list(self, request, *args, **kwargs):
        """List audit logs."""
        if AuditLog is None:
            # Return mock data if AuditLog is not available
            return Response({
                'count': 1,
                'results': [{
                    'id': 1,
                    'user': {
                        'id': request.user.id if request.user.is_authenticated else None,
                        'email': request.user.email if request.user.is_authenticated else None,
                        'name': request.user.get_full_name() if request.user.is_authenticated else None
                    },
                    'action': 'API_ACCESS',
                    'resource_type': 'security_audit_logs',
                    'resource_id': None,
                    'ip_address': request.META.get('REMOTE_ADDR', '127.0.0.1'),
                    'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                    'details': 'Audit log access',
                    'severity': 'INFO',
                    'timestamp': timezone.now()
                }]
            })

        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)

        if page is not None:
            data = []
            for log in page:
                data.append({
                    'id': log.id,
                    'user': {
                        'id': log.user.id if log.user else None,
                        'email': log.user.email if log.user else None,
                        'name': log.user.get_full_name() if log.user else None
                    },
                    'action': log.action,
                    'resource_type': getattr(log, 'resource_type', 'unknown'),
                    'resource_id': getattr(log, 'resource_id', None),
                    'ip_address': getattr(log, 'ip_address', ''),
                    'user_agent': getattr(log, 'user_agent', ''),
                    'details': getattr(log, 'details', ''),
                    'severity': getattr(log, 'severity', 'INFO'),
                    'timestamp': log.timestamp
                })

            return self.get_paginated_response(data)

        return Response([])


class SecurityMetricsView(APIView):
    """Security metrics endpoint."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @extend_schema(
        summary="Security Metrics",
        description="Get detailed security metrics",
        responses={200: {"description": "Security metrics data"}},
        tags=["Security"]
    )
    def get(self, request):
        """Get security metrics."""
        from django.db import models
        
        # Time ranges
        now = timezone.now()
        last_24h = now - timedelta(hours=24)
        last_7d = now - timedelta(days=7)
        last_30d = now - timedelta(days=30)
        
        # Action distribution (equivalent to event_type)
        actions = SecurityEvent.objects.filter(
            created_at__gte=last_30d
        ).values('event_type').annotate(
            count=models.Count('id')
        ).order_by('-count')

        # Risk level distribution
        risk_levels = SecurityEvent.objects.filter(
            created_at__gte=last_30d
        ).values('risk_level').annotate(
            count=models.Count('id')
        ).order_by('-count')

        # Daily event counts for the last 30 days
        daily_events = []
        for i in range(30):
            date = (now - timedelta(days=i)).date()
            count = SecurityEvent.objects.filter(
                created_at__date=date
            ).count()
            daily_events.append({
                'date': date,
                'count': count
            })
        
        return Response({
            'actions': list(actions),
            'risk_levels': list(risk_levels),
            'daily_events': daily_events,
            'generated_at': now
        })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def analyze_request_security(request):
    """Analyse de sécurité d'une requête"""

    request_data = {
        'ip_address': request.META.get('REMOTE_ADDR', '127.0.0.1'),
        'user_agent': request.META.get('HTTP_USER_AGENT', ''),
        'method': request.method,
        'path': request.path,
        'query_params': dict(request.GET),
        'post_params': dict(request.POST) if request.method == 'POST' else {}
    }

    # Analyser la requête avec le système IDS
    analysis = ids.analyze_request(request_data)

    return Response({
        'analysis': analysis,
        'timestamp': timezone.now().isoformat()
    })


@api_view(['GET'])
@permission_classes([permissions.IsAdminUser])
def compliance_status(request):
    """Statut de conformité"""

    framework = request.GET.get('framework', 'ISO_27001')

    try:
        assessment = compliance_framework.assess_compliance(framework)
        return Response(assessment)
    except ValueError as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )


@api_view(['POST'])
@permission_classes([permissions.IsAdminUser])
def trigger_security_scan(request):
    """Déclencher un scan de sécurité"""

    scan_type = request.data.get('scan_type', 'basic')

    # Simulation d'un scan de sécurité
    scan_result = {
        'scan_id': crypto.generate_secure_token(16),
        'scan_type': scan_type,
        'status': 'initiated',
        'started_at': timezone.now().isoformat(),
        'estimated_duration': '5-10 minutes'
    }

    return Response(scan_result, status=status.HTTP_202_ACCEPTED)
